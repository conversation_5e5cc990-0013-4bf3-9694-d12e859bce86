import { type FC, ReactNode, useState } from 'react';
import {
  CloseCircleOutlined,
  MinusCircleOutlined,
  MinusOutlined,
  PlusCircleOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import {
  EditableProTable,
  ProForm,
  ProFormCheckbox,
  ProFormDependency,
  ProFormDigit,
  ProFormList,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { Button, Cascader, CascaderProps, Checkbox, Divider, FormInstance, Input, Space } from 'antd';
import { CascaderRef } from 'antd/es/cascader';
import classNames from 'classnames';
import { cloneDeep } from 'lodash';
import { v4 } from 'uuid';
import SopSelect from '@/components/sop-select';
import {
  CheckItemAttribute,
  DeductType,
  DeductTypeCN,
  FractionalType,
  FractionalTypeCN,
  ProcessingActionType,
  ProcessingActionTypeCN,
} from '@/constants/checklist';
import useQuerySearchParams from '@/hooks/use-query-search-params';
import { ICheckItemGroupTypeListItem, useCheckItemGroupByType } from '@/http/apis/checkItem';

const ScoreTypeIconMap: Record<FractionalType, ReactNode> = {
  [FractionalType.Score]: <PlusOutlined style={{ color: '#95de64' }} />,
  [FractionalType.Deduction]: <MinusOutlined style={{ color: '#ff7875' }} />,
};

const { Item: FormItem } = ProForm;

export const CheckItemCategory: FC<any> = ({ options, name, onCreate }: any) => {
  const [categoryName, setCategoryName] = useState<string | undefined>();

  return (
    <ProFormSelect
      label="检查项分类"
      name={name}
      width="xl"
      rules={[
        {
          required: true,
          message: '请选择分类',
        },
      ]}
      fieldProps={{
        options,
        showSearch: true,
        dropdownRender: (menu) => {
          return (
            <>
              {menu}
              <Divider style={{ margin: '8px 0' }} />
              <Space style={{ padding: '0 8px 4px' }}>
                <Input
                  value={categoryName}
                  placeholder="请输入检查项分类名称"
                  onChange={(e: any) => {
                    setCategoryName(e.target.value);
                  }}
                />
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => {
                    onCreate?.(categoryName).then(() => {
                      setCategoryName(undefined);
                    });
                  }}
                >
                  新增分类
                </Button>
              </Space>
            </>
          );
        },
      }}
    />
  );
};

export const CheckItemContent: FC<any> = ({ name }) => {
  return (
    <ProFormTextArea
      label="检查项内容"
      width="xl"
      name={name}
      rules={[
        {
          required: true,
          transform: (v) => (v ? v.trim() : v),
          message: '请输入详细的检查内容，最多400字',
        },
      ]}
      placeholder="请输入详细的检查内容，最多400字"
      fieldProps={{
        maxLength: 400,
        showCount: true,
      }}
    />
  );
};

type RelevancyCheckItemProps = Partial<{
  label: string;
  name: string;
  cascaderProps: React.PropsWithChildren<CascaderProps<ICheckItemGroupTypeListItem>> & React.RefAttributes<CascaderRef>;
}>;

export function RelevancyCheckItem({ label, name, cascaderProps }: RelevancyCheckItemProps) {
  const { data, loading } = useCheckItemGroupByType();
  const [params] = useQuerySearchParams<{ itemId: string }>();
  const disabled = !!params?.itemId;

  const labelText = label || '关联检查项';

  return (
    <FormItem
      label={labelText}
      name={name || 'associationCheckItemId'}
      rules={[
        {
          required: true,
          message: `请选择${labelText}`,
        },
      ]}
    >
      <Cascader
        style={{ width: 552 }}
        placeholder={`请选择${labelText}`}
        fieldNames={{ label: 'name', value: 'id', children: 'items' }}
        options={data || []}
        showSearch
        loading={loading}
        disabled={disabled}
        {...cascaderProps}
      />
    </FormItem>
  );
}

const ImportantItemCheckboxGroup: FC<any> = ({ value, onChange, radioName, typeName, form, isPOSITIVE }) => {
  const type = ProForm.useWatch(typeName, form);

  const checkedItem = (key: any, checked: boolean) => {
    const cloneItems = cloneDeep(value) || [];

    if (checked) {
      cloneItems.push(key);
    } else {
      cloneItems.splice(cloneItems.indexOf(key), 1);
    }

    onChange?.(cloneItems);
  };

  return (
    <Space direction="vertical" size={0}>
      <div>
        <div className="leading-8">
          <Checkbox
            checked={value?.includes(CheckItemAttribute.RED_LINE)}
            onChange={(e) => {
              checkedItem(CheckItemAttribute.RED_LINE, e.target.checked);
            }}
          >
            S项
          </Checkbox>
        </div>
      </div>
      <div>
        <div className="leading-8">
          <Checkbox
            checked={value?.includes(CheckItemAttribute.YELLOW)}
            onChange={(e) => {
              checkedItem(CheckItemAttribute.YELLOW, e.target.checked);
            }}
          >
            M项
          </Checkbox>
        </div>
        {/* <div className="text-[rgba(0, 0, 0, 0.45)] min-h-[24px] leading-6">若此项不通过整个分类的得分扣除100%</div> */}
      </div>
      <div>
        <div className="leading-8">
          <Checkbox
            checked={value?.includes(CheckItemAttribute.NECESSARY)}
            onChange={(e) => {
              checkedItem(CheckItemAttribute.NECESSARY, e.target.checked);
            }}
          >
            必检项
          </Checkbox>
        </div>
        <div
          className="min-h-[24px] leading-6"
          style={{
            color: 'rgba(0, 0, 0, 0.45)',
          }}
        >
          若此项未检查，则无法提交报告
        </div>
      </div>

      <div>
        <div className="leading-8">
          <Checkbox
            checked={value?.includes(CheckItemAttribute.KEY)}
            onChange={(e) => {
              checkedItem(CheckItemAttribute.KEY, e.target.checked);
            }}
          >
            关键项
          </Checkbox>
        </div>
        {/* <div className="text-[rgba(0, 0, 0, 0.45)] min-h-[24px] leading-6">若此项不通过整个分类的得分扣除100%</div> */}
      </div>
      <div>
        <div className="leading-8">
          <Checkbox
            checked={value?.includes(CheckItemAttribute.KEY)}
            onChange={(e) => {
              checkedItem(CheckItemAttribute.KEY, e.target.checked);
            }}
          >
            关键项
          </Checkbox>
        </div>
        {/* <div className="text-[rgba(0, 0, 0, 0.45)] min-h-[24px] leading-6">若此项不通过整个分类的得分扣除100%</div> */}
      </div>
      {!!isPOSITIVE && (
        <div>
          <div className="leading-8">
            <Checkbox
              checked={value?.includes(CheckItemAttribute.POSITIVE)}
              onChange={(e) => {
                checkedItem(CheckItemAttribute.POSITIVE, e.target.checked);
              }}
            >
              阳性指标项
            </Checkbox>
          </div>
          <div className="text-[rgba(0, 0, 0, 0.45)] min-h-[24px] leading-6">
            若此项不通过整个报告打【阳性报告】标签
          </div>
        </div>
      )}
    </Space>
  );
};

export const ImportantItem: FC<any> = ({
  form,
  name,
  typeName,
  radioName,
  ruleName,
  /** 是否显示关键项设置 默认显示 */
  isShowKeyItem = true,
}) => {
  const deductionRule = ProForm.useWatch(ruleName, form);
  const type = ProForm.useWatch(typeName, form);

  return (
    <>
      {isShowKeyItem && (
        <FormItem label="重点项设置" name={name} initialValue={[]}>
          <ImportantItemCheckboxGroup typeName={typeName} radioName={radioName} form={form} />
        </FormItem>
      )}
      <ProFormRadio.Group
        label="额外扣分规则"
        name={ruleName}
        initialValue={false}
        fieldProps={{
          onChange: (e) => {
            if (e.target.value) {
              form?.setFieldValue(typeName, DeductType.ALL_CATEGORY_SCORE);
              form.setFieldValue(radioName, 100);
            } else {
              form?.setFieldValue(typeName, undefined);
              form.setFieldValue(radioName, undefined);
            }
          },
        }}
        options={[
          {
            label: '无',
            value: false,
          },
          {
            label: '有',
            value: true,
          },
        ]}
        extra={
          deductionRule && (
            <div className="flex  min-h-[24px]  items-start">
              <span className="leading-8 text-[#000]">若此项不通过，将对</span>
              <ProFormSelect
                name={typeName}
                width={150}
                allowClear={false}
                initialValue={DeductType.ALL_CATEGORY_SCORE}
                fieldProps={{
                  options: Object.entries(DeductTypeCN).map(([value, label]) => ({
                    label,
                    value,
                  })),
                  onSelect: (value: any) => {
                    if (value !== DeductType.REPORT_SCORE) {
                      form.setFieldValue(radioName, 100);
                    } else {
                      form.setFieldValue(radioName, undefined);
                    }
                  },
                }}
              />

              <span className="leading-8 text-[#000]">扣除</span>
              <ProFormDigit
                width="xs"
                name={radioName}
                min={1}
                initialValue={100}
                max={100}
                rules={[
                  {
                    required: true,
                    message: '请输入1-100之间的整数',
                  },
                ]}
                fieldProps={{
                  precision: 0,
                  suffix: '%',
                }}
                disabled={type !== DeductType.REPORT_SCORE}
              />
            </div>
          )
        }
      />
    </>
  );
};

export const AssociatedSOP: FC<any> = ({ tagOptions, onTagChange, sopOptions, name, className }: any) => {
  return (
    <FormItem label="关联SOP" name={name}>
      <SopSelect tagOptions={tagOptions} onTagChange={onTagChange} sopOptions={sopOptions} sopClassName={className} />
    </FormItem>
  );
};

export const ImageOrVideoUpload: FC<any> = ({
  normalName,
  rectifyName,
  abnormalName,
  initialValue,
  rectifyDisabled = false,
}) => {
  return (
    <FormItem label="图片/视频上传设置">
      <Space direction="vertical">
        {abnormalName && (
          <FormItem name={abnormalName} valuePropName="checked" noStyle initialValue={initialValue?.[abnormalName]}>
            <Checkbox>不通过项必须上传照片或者视频</Checkbox>
          </FormItem>
        )}
        {rectifyName && (
          <FormItem name={rectifyName} valuePropName="checked" noStyle initialValue={initialValue?.[rectifyName]}>
            <Checkbox disabled={rectifyDisabled}>整改必须上传整改照片或视频</Checkbox>
          </FormItem>
        )}
        {normalName && (
          <FormItem name={normalName} valuePropName="checked" noStyle initialValue={initialValue?.[normalName]}>
            <Checkbox>通过项必须上传整改照片或视频</Checkbox>
          </FormItem>
        )}
      </Space>
    </FormItem>
  );
};

export const UploadMobileAlbum: FC<any> = ({ name, extra }) => {
  return (
    <ProFormRadio.Group
      name={name}
      label="上传相册图片/视频"
      initialValue={'FOLLOWING_SYSTEM'}
      options={[
        {
          label: `跟随系统设置`,
          value: 'FOLLOWING_SYSTEM',
        },
        {
          label: `允许`,
          value: 'ALLOW',
        },
        {
          label: `不允许`,
          value: 'NOT_ALLOW',
        },
      ]}
      extra={extra}
    />
  );
};

export const ScoringStandard: FC<any> = ({ name }) => {
  return (
    <ProFormTextArea
      label="评分标准"
      name={name}
      width="xl"
      placeholder="请输入详细的评分标准，最多500字"
      fieldProps={{
        maxLength: 500,
        showCount: true,
      }}
    />
  );
};

export const DisqualificationReason: FC<any> = ({
  name,
  form,
  mustName,
  label = '不通过原因',
  checkboxLabel = '不通过项必须选择不通过原因',
  studyOptions,
  tagOptions = [],
  isPatrol = false,
}) => {
  const reasons = ProForm.useWatch(name, form);

  return (
    <>
      <ProFormList
        name={name}
        label={label}
        copyIconProps={false}
        initialValue={[{}]}
        deleteIconProps={
          reasons?.length > 1
            ? {
                Icon: CloseCircleOutlined,
              }
            : false
        }
        creatorButtonProps={{
          position: 'bottom',
          creatorButtonText: '新增原因',
          style: {
            width: 552,
          },
        }}
      >
        <div className="flex">
          {!isPatrol ? (
            <ProFormText
              placeholder="请输入不通过原因，最多30字"
              name={'reason'}
              width={studyOptions && studyOptions?.length > 0 ? 276 : 'xl'}
              fieldProps={{
                maxLength: 30,
                showCount: true,
                onChange: (e: any) => {
                  if (!e.target.value && reasons?.length === 1) {
                    form.setFieldValue(mustName, false);
                  } else {
                    form.setFieldValue(mustName, true);
                  }
                },
              }}
            />
          ) : (
            <ProFormSelect
              name={'reasonId'}
              options={tagOptions}
              width={studyOptions && studyOptions?.length > 0 ? 276 : 'xl'}
              fieldProps={{
                onChange: (value) => {
                  if (!value && reasons?.length === 1) {
                    form.setFieldValue(mustName, false);
                  } else {
                    form.setFieldValue(mustName, true);
                  }
                },
                allowClear: true,
              }}
              showSearch={true}
            />
          )}
          {studyOptions && studyOptions?.length > 0 && (
            <ProFormDependency key={'studyProjectId'} name={['reasonId']}>
              {({ reasonId }) => {
                return (
                  <ProFormSelect
                    name={'studyProjectId'}
                    disabled={!reasonId}
                    width={studyOptions && studyOptions?.length > 0 ? 276 : 'xl'}
                    options={studyOptions}
                    fieldProps={{
                      showSearch: true,
                      placeholder: '请选择学习项目',
                    }}
                  />
                );
              }}
            </ProFormDependency>
          )}
        </div>
      </ProFormList>
      <ProFormDependency key={mustName} name={[name]}>
        {() => {
          return (
            !(reasons?.length === 1 && (isPatrol ? !reasons?.[0]?.reasonId : !reasons?.[0]?.reason)) && (
              <ProFormCheckbox label=" " colon={false} name={mustName} valuePropName="checked">
                {checkboxLabel}
              </ProFormCheckbox>
            )
          );
        }}
      </ProFormDependency>
    </>
  );
};

export const SelfUnqualifiedReason: FC<any> = ({
  name,
  form,
  mustName,
  label = '不通过原因',
  checkboxLabel = '不通过项必须选择不通过原因',
  isMustReason = true,
  tagOptions = [],
  isCopy,
}) => {
  return (
    <>
      <ProFormSelect
        width="md"
        label={label}
        name={name}
        extra={
          isCopy && (
            <a
              onClick={() => {
                const firstReason = form.getFieldsValue()?.reviewAbnormalReasons;

                form.setFieldsValue({ secondReviewAbnormalReasons: firstReason });
              }}
            >
              复制第一点评原因
            </a>
          )
        }
        options={tagOptions}
        mode="multiple"
        showSearch
        allowClear
        placeholder="请选择不通过原因"
      />

      <ProFormDependency key={mustName} name={[name]}>
        {() => {
          return (
            isMustReason && (
              <ProFormCheckbox label=" " colon={false} name={mustName} valuePropName="checked">
                {checkboxLabel}
              </ProFormCheckbox>
            )
          );
        }}
      </ProFormDependency>
    </>
  );
};

export enum ReviewMethodType {
  MANUAL_JUDGMENT = 'MANUAL_JUDGMENT',
  MANUAL_SCORING = 'MANUAL_SCORING',
  NO_REVIEWS_NEEDED = 'NO_REVIEWS_NEEDED',
}

export enum ReviewMethodTypeCN {
  MANUAL_JUDGMENT = '人工判断型',
  MANUAL_SCORING = '人工打分型',
  NO_REVIEWS_NEEDED = '不需要点评',
}

export const ReviewMethod: FC<any> = ({ name, extra, onChange }) => {
  return (
    <ProFormRadio.Group
      name={name}
      label="点评方式"
      extra={extra}
      initialValue={ReviewMethodType.MANUAL_JUDGMENT}
      fieldProps={{
        onChange,
      }}
      options={[
        {
          label: ReviewMethodTypeCN[ReviewMethodType.MANUAL_JUDGMENT],
          value: ReviewMethodType.MANUAL_JUDGMENT,
        },
        {
          label: ReviewMethodTypeCN[ReviewMethodType.MANUAL_SCORING],
          value: ReviewMethodType.MANUAL_SCORING,
        },
        {
          label: ReviewMethodTypeCN[ReviewMethodType.NO_REVIEWS_NEEDED],
          value: ReviewMethodType.NO_REVIEWS_NEEDED,
        },
      ]}
    />
  );
};

export const JudgeButtonName: FC<any> = ({ normalName, abnormalName, form }) => {
  return (
    <FormItem label="判断按钮文案" required>
      <div className="flex items-center">
        <span className="mr-2">正常状态按钮文案</span>
        <ProFormText
          placeholder="请输入"
          name={normalName}
          fieldProps={{ maxLength: 6 }}
          rules={[{ required: true, message: '请输入正常状态按钮文案' }]}
          noStyle
          width="xs"
          initialValue={'合格'}
        />
        <span className="mr-2">非正常状态按钮文案</span>
        <ProFormText
          placeholder="请输入"
          name={abnormalName}
          fieldProps={{ maxLength: 6 }}
          rules={[{ required: true, message: '请输入非正常状态按钮文案' }]}
          noStyle
          width="xs"
          initialValue={'不合格'}
        />
      </div>
    </FormItem>
  );
};

export const Score: FC<any> = ({ form, typeName, scoreName, deductionDisabled = false }: any) => {
  const itemScore = ProForm.useWatch(scoreName, form);
  const scoreType = ProForm.useWatch(typeName, form);
  const reportReviewMethod = ProForm.useWatch('reportReviewMethod', form);
  const type = ProForm.useWatch('type', form);

  return (
    <div className="tstd-form-item-hide-required">
      <ProFormRadio.Group
        name={typeName}
        label="分数"
        layout="vertical"
        initialValue={FractionalType.Score}
        required
        options={[
          {
            label: `${FractionalTypeCN[FractionalType.Score]}（通过得分，不通过不得分）`,
            value: FractionalType.Score,
          },
          {
            label: `${FractionalTypeCN[FractionalType.Deduction]}（通过不扣分，不通过则扣减对应分数）`,
            disabled: deductionDisabled || reportReviewMethod === 'MANUAL_JUDGMENT' || type === 'JUDGE',
            value: FractionalType.Deduction,
          },
        ]}
        extra={
          <div className="mt-2">
            <ProFormDigit
              width="sm"
              name={scoreName}
              extra={itemScore === '0' && '满分为0意味着该项通过与不通过都得0分，建议仅在该检查项为S项时使用。'}
              style={{ marginBottom: 0, marginTop: 10 }}
              min={0}
              rules={[
                {
                  required: true,
                  message: '请输入分数',
                },
              ]}
              max={100}
              fieldProps={{
                prefix: ScoreTypeIconMap[scoreType as FractionalType],
                step: '0.01',
                stringMode: true,
                precision: 2,
              }}
            />
          </div>
        }
      />
    </div>
  );
};

export const DisplayScore: FC<any> = ({ name }) => {
  return (
    <ProFormRadio.Group
      name={name}
      label="判断按钮显示分数"
      initialValue={false}
      options={[
        {
          label: `不显示`,
          value: false,
        },
        {
          label: `显示`,
          value: true,
        },
      ]}
    />
  );
};

export const ValidityCheckItem: FC = () => {
  return (
    <FormItem>
      <p className="text-[#B8B8B8]">系统目前仅支持对效期标签自动识别判断，若非效期标签请勿使用！</p>
      <EditableProTable
        rowKey="id"
        recordCreatorProps={{
          record: () => ({ id: v4() }),
          style: {
            display: 'none',
          },
        }}
        controlled={true}
        editable={{
          type: 'multiple',
          actionRender: () => {
            return [<MinusCircleOutlined className="text-lg" />, <PlusCircleOutlined className="text-lg" />];
          },
        }}
        columns={[
          {
            title: '检查项',
            dataIndex: 'checkItem',
            fieldProps: {
              placeholder: '请输入检查项',
              maxLength: 10,
              showCount: true,
            },
            formItemProps: () => {
              return {
                rules: [{ required: true, message: '此项为必填项' }],
              };
            },
          },
          {
            title: '检查项说明',
            dataIndex: 'checkContent',
            fieldProps: {
              placeholder: '请输入检查项说明',
              maxLength: 30,
              showCount: true,
            },
          },
          {
            title: '检查项分类',
            dataIndex: 'category',
            formItemProps: () => {
              return {
                rules: [{ required: true, message: '此项为必填项' }],
              };
            },
            valueType: 'select',
          },
          {
            title: '',
            valueType: 'option',
            width: 64,
            render: () => {
              return null;
            },
          },
        ]}
      />
    </FormItem>
  );
};

export const ProcessingAction: FC<any> = ({
  name,
  form,
  uploadName,
  tipName,
  normalName,
  abnormalName,
  reasonName,
  mustName,
  followName,
  followPhotoName,
  followUploadName,
  tagOptions,
  sopOptions,
  onTagChange,
  followSopName,
  followTipName,
  onChange,
}) => {
  const action = ProForm.useWatch(name, form);
  const followAction = ProForm.useWatch(followName, form);

  return (
    <>
      <ProFormRadio.Group
        name={name}
        label="处理动作"
        initialValue={ProcessingActionType.PHOTOGRAPH}
        fieldProps={{
          onChange: (e) => {
            onChange?.(e.target.value);

            if (e.target.value === ProcessingActionType.PHOTOGRAPH) {
              // 切换拍照时，设置上传相册图片/视频默认为：跟随系统设置
              form.setFieldValue(uploadName, 'FOLLOWING_SYSTEM');
            } else if (e.target.value === ProcessingActionType.JUDGE) {
              // 切换拍照时，判断按钮文案：合格  不合格
              form.setFieldValue(normalName, '合格');
              form.setFieldValue(abnormalName, '不合格');
            }
          },
        }}
        options={Object.keys(ProcessingActionTypeCN).map((key) => ({ value: key, label: ProcessingActionTypeCN[key] }))}
      />
      {action !== ProcessingActionType.GAP_FILLING && (
        <ProFormDigit name="minimumImages" label="最少上传照片张数" width="sm" min={1} max={9} allowClear />
      )}
      {action === ProcessingActionType.PHOTOGRAPH && (
        <>
          <UploadMobileAlbum name={uploadName} />
          <ProFormText
            name="photoRemark"
            label="拍照备注"
            width="xl"
            placeholder="选填，不超过15字符"
            fieldProps={{ maxLength: 15 }}
          />
        </>
      )}
      {action === ProcessingActionType.GAP_FILLING && <PromptName name={tipName} width="xl" />}
      {action === ProcessingActionType.JUDGE && (
        <>
          <JudgeButtonName normalName={normalName} abnormalName={abnormalName} form={form} />
          <DisqualificationReason
            name={reasonName}
            mustName={mustName}
            form={form}
            label="非正常原因配置"
            checkboxLabel="非正常结果必须选择原因"
          />
          <div className={classNames({ 'tstd-form-item-hide-required': followAction !== 'NOT_ACTION' })}>
            <ProFormRadio.Group
              name={followName}
              label="后续动作"
              initialValue={'NOT_ACTION'}
              options={[
                {
                  label: '无',
                  value: 'NOT_ACTION',
                },
                {
                  label: ProcessingActionTypeCN[ProcessingActionType.PHOTOGRAPH],
                  value: ProcessingActionType.PHOTOGRAPH,
                },
                {
                  label: ProcessingActionTypeCN[ProcessingActionType.GAP_FILLING],
                  value: ProcessingActionType.GAP_FILLING,
                },
              ]}
              fieldProps={{
                onChange: (e: any) => {
                  const { value } = e.target;

                  if (value === ProcessingActionType.PHOTOGRAPH) {
                    form.setFieldValue(followUploadName, 'FOLLOWING_SYSTEM');
                  }

                  // 目前后续动作的拍照和填空共享字段，因此切换后续动作时，重置值
                  if (value !== 'NOT_ACTION') {
                    form.setFieldValue(followPhotoName, 'ALL');
                  }
                },
              }}
              extra={
                followAction === 'NOT_ACTION' ? (
                  false
                ) : (
                  <>
                    {followAction === ProcessingActionType.PHOTOGRAPH && (
                      <>
                        <div className="my-6">
                          <ProFormRadio.Group
                            noStyle
                            name={followPhotoName}
                            initialValue={'ALL'}
                            options={[
                              {
                                label: '都拍照',
                                value: 'ALL',
                              },
                              {
                                label: '正常结果拍照',
                                value: 'WHEN_NORMAL',
                              },
                              {
                                label: '非正常结果拍照',
                                value: 'WHEN_ABNORMAL',
                              },
                            ]}
                          />
                        </div>
                        <PhotoRemark name="afterActionPhoneMark" form={form} followPhotoName={followPhotoName} />
                        <UploadMobileAlbum name={followUploadName} />
                        <AssociatedSOP
                          name={followSopName}
                          className="w-[484px]"
                          tagOptions={tagOptions}
                          sopOptions={sopOptions}
                          onTagChange={onTagChange}
                        />
                      </>
                    )}
                    {followAction === ProcessingActionType.GAP_FILLING && (
                      <>
                        {/* 和后端确认后续动作：拍照和填空共享字段 */}
                        <div className="my-6">
                          <ProFormRadio.Group
                            noStyle
                            name={followPhotoName}
                            initialValue={'ALL'}
                            options={[
                              {
                                label: '都填空',
                                value: 'ALL',
                              },
                              {
                                label: '正常结果填空',
                                value: 'WHEN_NORMAL',
                              },
                              {
                                label: '非正常结果填空',
                                value: 'WHEN_ABNORMAL',
                              },
                            ]}
                          />
                        </div>
                        <div className="w-[552px]">
                          <PromptName name={followTipName} />
                        </div>
                      </>
                    )}
                  </>
                )
              }
            />
          </div>
        </>
      )}
    </>
  );
};

export const AbnormalReason: FC = () => {
  return (
    <>
      <ProFormList
        name="nonconformityReasons"
        label="非正常原因配置"
        copyIconProps={false}
        initialValue={[{}]}
        // deleteIconProps={
        //   nonconformityReasons?.length > 1
        //     ? {
        //         Icon: CloseCircleOutlined
        //       }
        //     : false
        // }
        creatorButtonProps={{
          position: 'bottom',
          creatorButtonText: '新增原因',
        }}
      >
        <ProFormText
          placeholder="请输入非正常原因，最多30字"
          name={'reason'}
          fieldProps={{
            maxLength: 30,
            showCount: true,
            onChange: (e: any) => {
              //   if (!e.target.value && nonconformityReasons?.length === 1) {
              //     form.setFieldValue('mustChooseNonconformityReasons', false);
              //   } else {
              //     form.setFieldValue('mustChooseNonconformityReasons', true);
              //   }
            },
          }}
        />
      </ProFormList>
      <ProFormDependency key="mustChooseNonconformityReasons" name={['nonconformityReasons']}>
        {({ nonconformityReasons }) => {
          return (
            !(nonconformityReasons?.length === 1 && !nonconformityReasons?.[0]?.reason) && (
              <ProFormCheckbox label=" " colon={false} name="mustChooseNonconformityReasons" valuePropName="checked">
                非正常结果必须选择原因
              </ProFormCheckbox>
            )
          );
        }}
      </ProFormDependency>
    </>
  );
};

export const PromptName: FC<any> = ({ name, width }) => {
  return (
    <ProFormText
      name={name}
      width={width}
      label="输入框提醒文案"
      placeholder="请输入提醒文案，如：请输入预估营业额"
      fieldProps={{ maxLength: 150 }}
    />
  );
};

export const FollowUp: FC = () => {
  return (
    <ProFormRadio.Group
      name="allowAlbum"
      label="后续动作"
      initialValue={0}
      options={[
        {
          label: `无`,
          value: 0,
        },
        {
          label: `拍照`,
          value: 1,
        },
        {
          label: `填空`,
          value: 2,
        },
      ]}
      extra={
        <>
          <ProFormRadio.Group
            name="allowAlbum"
            noStyle
            initialValue={0}
            options={[
              {
                label: `都拍照`,
                value: 0,
              },
              {
                label: `正常结果拍照`,
                value: 1,
              },
              {
                label: `非正常结果拍照`,
                value: 2,
              },
            ]}
          />
          <UploadMobileAlbum />
          <AssociatedSOP />
        </>
      }
    />
  );
};

export function PhotoRemark({
  name,
  form,
  followPhotoName,
  photoNormalMarkName = 'afterActionPhoneNormalMark',
  photoAbnormalMarkName = 'afterActionPhoneAbnormalMark',
}: {
  name: string;
  form: FormInstance<any>;
  followPhotoName: string;
  photoNormalMarkName?: string;
  photoAbnormalMarkName?: string;
}) {
  const followPhoto = ProForm.useWatch(followPhotoName, form);
  const phoneRemark = ProForm.useWatch(name, form);

  const isAllPhoto = followPhoto === 'ALL';

  return (
    <ProFormRadio.Group
      name={name}
      label="拍照备注"
      initialValue={false}
      options={[
        { label: `否`, value: false },
        { label: `是`, value: true },
      ]}
      extra={
        phoneRemark && (
          <div className="mt-6 flex gap-x-3">
            {(isAllPhoto || followPhoto === 'WHEN_NORMAL') && (
              <ProFormText
                name={photoNormalMarkName}
                label="正常结果"
                width="sm"
                placeholder="请输入少于15字的拍照备注"
                fieldProps={{ maxLength: 15 }}
              />
            )}
            {(isAllPhoto || followPhoto === 'WHEN_ABNORMAL') && (
              <ProFormText
                name={photoAbnormalMarkName}
                label="非正常结果"
                width="sm"
                placeholder="请输入少于15字的拍照备注"
                fieldProps={{ maxLength: 15 }}
              />
            )}
          </div>
        )
      }
    />
  );
}

enum OilCylinderEnum {
  一号双缸左 = 'ONE_DOUBLE_LEFT',
  一号双缸右 = 'ONE_DOUBLE_RIGHT',
  二号双缸左 = 'TWO_DOUBLE_LEFT',
  二号双缸右 = 'TWO_DOUBLE_RIGHT',
  一号单缸大 = 'ONE_SINGLE_BIG',
  二号单缸大 = 'TWO_SINGLE_BIG',
  三号单缸大 = 'THREE_SINGLE_BIG',
}

const oilCylinderOptions = [
  { label: '1号双缸（左）', value: OilCylinderEnum.一号双缸左 },
  { label: '1号双缸（右）', value: OilCylinderEnum.一号双缸右 },
  { label: '2号双缸（左）', value: OilCylinderEnum.二号双缸左 },
  { label: '2号双缸（右）', value: OilCylinderEnum.二号双缸右 },
  { label: '1号单缸（大）', value: OilCylinderEnum.一号单缸大 },
  { label: '2号单缸（大）', value: OilCylinderEnum.二号单缸大 },
  { label: '3号单缸（大）', value: OilCylinderEnum.三号单缸大 },
];

export function OilConfig({ name, form }: { name: string; form: FormInstance<any> }) {
  const oilMarkChecked = ProForm.useWatch(name, form);

  return (
    <ProFormCheckbox
      label="标记"
      name={name}
      valuePropName="checked"
      initialValue={false}
      fieldProps={{
        onChange: ({ target }) => {
          form.setFieldValue(name, target.checked);
        },
      }}
      extra={
        oilMarkChecked && (
          <div className="mt-6">
            <ProFormSelect
              name="oilCylinderName"
              label="油缸名称"
              width="sm"
              options={oilCylinderOptions}
              rules={[
                {
                  required: true,
                  message: '请选择油缸名称',
                },
              ]}
            />
          </div>
        )
      }
    >
      测油/换油记录表
    </ProFormCheckbox>
  );
}

export function FrequencyConfig({ name, form }: { name: string; form: FormInstance<any> }) {
  const frequencyJudgeChecked = ProForm.useWatch(name, form);

  return (
    <ProFormCheckbox
      label="出现频次配置"
      name={name}
      valuePropName="checked"
      initialValue={false}
      extra={
        frequencyJudgeChecked && (
          <div className="mt-6">
            <ProFormRadio.Group
              name="frequency"
              initialValue={false}
              options={[
                {
                  label: '正常',
                  value: true,
                },
                {
                  label: '非正常',
                  value: false,
                },
              ]}
              extra={
                <div className="mt-6">
                  <div className="flex items-center gap-x-3 my-6 ml-11">
                    <ProFormSelect
                      noStyle
                      name="frequencyCount"
                      colon={false}
                      width="sm"
                      initialValue={1}
                      allowClear={false}
                      options={Array.from({ length: 5 }, (_, idx) => ({ label: `${idx + 1}次`, value: idx + 1 }))}
                    />
                    <span className="text-[#3D3D3D]">无需操作该检查项</span>
                  </div>
                  <ProFormText
                    label="备注"
                    name="frequencyMark"
                    width="sm"
                    placeholder="请输入备注，不超过8字符"
                    fieldProps={{
                      maxLength: 8,
                    }}
                  />
                </div>
              }
            />
          </div>
        )
      }
    >
      判断设置
    </ProFormCheckbox>
  );
}

export const FullScore = ({ name }) => {
  return (
    <ProFormDigit
      label="满分分数"
      name={name}
      width="xs"
      min={0}
      max={100}
      rules={[
        {
          required: true,
          message: '请输入满分分数',
        },
      ]}
      transform={(value) => {
        return {
          scoreType: 'ADD_SCORE',
          score: value,
        };
      }}
    />
  );
};

export const Solution = ({ name }) => {
  return (
    <ProFormCheckbox.Group
      name={name}
      layout="vertical"
      label="处理方案"
      options={[{ label: '不通过时选择门店自行处理还是需工程封堵', value: true }]}
      extra={'说明：日常消杀、紧急消杀业务中，当不通过的检查项的处理方案为【需工程封堵】时系统才会发起问题整改任务。'}
      transform={(value) => {
        return !!value?.[0];
      }}
    />
  );
};
