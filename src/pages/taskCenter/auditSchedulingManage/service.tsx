import useServiceReducer from '@/hooks/use-service-reducer';
import HttpAop from '@/http/aop';
import { getGroupTreeList, getStoreList, getUserInfoList } from '@/http/apis/center-control';
import {
  getAuditRouteByBatchId,
  getAuditRouteDataPageList,
  getFoodSafetyNormalRouteList,
  getRouteIdsByBatchDate,
  modifyAuditRouteData,
} from '@/http/apis/task-center';
import { formatOrganizationTreeToOptions, formatShopsToOptions, formatUserListToRoleOptions } from '@/utils/format';

export enum RequestName {
  GetUserInfoList,
  GetFoodSafetyNormalRouteList,
  GetRouteIdsByBatchDate,
  GetAuditRouteByBatchId,
  GetAuditRouteDataPageList,
  GetStoreList,
  GetGroupTreeList,
  ModifyAuditRouteData,
}

export const initState: any = {
  routeIds: [],
};

const Service = () => {
  const [service, executeRequest, dispatch] = useServiceReducer(initState, {
    [RequestName.GetUserInfoList]: {
      beforeRequest: (dispatch) => {
        dispatch({ reviewerLoading: true });
      },
      request: HttpAop(getUserInfoList, {
        after: [(v) => formatUserListToRoleOptions(v, true)],
      }),
      afterRequest: (data, dispatch) => {
        dispatch({ reviewerOptions: data, reviewerLoading: false });
      },
    },
    [RequestName.GetFoodSafetyNormalRouteList]: {
      request: getFoodSafetyNormalRouteList,
    },
    [RequestName.GetRouteIdsByBatchDate]: {
      request: getRouteIdsByBatchDate,
      afterRequest: (data, dispatch) => {
        dispatch({
          routeIds: data?.map((ids) => {
            return {
              label: ids,
              value: ids,
            };
          }),
        });
      },
    },
    [RequestName.GetAuditRouteByBatchId]: {
      request: getAuditRouteByBatchId,
    },
    [RequestName.GetAuditRouteDataPageList]: {
      request: getAuditRouteDataPageList,
    },
    [RequestName.GetStoreList]: {
      request: HttpAop(getStoreList, {
        after: [formatShopsToOptions],
      }),
      afterRequest: (data, dispatch) => {
        dispatch({ storeOptions: data });
      },
    },
    [RequestName.GetGroupTreeList]: {
      request: HttpAop(getGroupTreeList, { after: [formatOrganizationTreeToOptions] }),
      afterRequest: (data, dispatch) => {
        dispatch({ organizationOptions: data });
      },
    },
    [RequestName.ModifyAuditRouteData]: {
      request: modifyAuditRouteData,
    },
  });

  return [service, executeRequest, dispatch];
};

export default Service;
