import { ProColumns } from '@ant-design/pro-components';
import { merge } from 'lodash';
import DateQuerySelect, { DateQuerySelectProps } from '@/components/date-query-select';

const getDateQueryColumn = (
  props?: Pick<DateQuerySelectProps, 'typeOptions' | 'showDefaultBeginPatrolTimeOption'>,
  column?: ProColumns,
) => {
  return merge(
    {
      title: '日期查询',
      dataIndex: 'dateQuery',
      hideInTable: true,
      colSize: 2.5,
      renderFormItem: () => {
        return <DateQuerySelect {...props} />;
      },
    },
    column,
  );
};

export default getDateQueryColumn;
