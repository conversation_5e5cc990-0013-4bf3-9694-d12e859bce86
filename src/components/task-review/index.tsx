import { FC, useCallback, useEffect, useState } from 'react';
import { Button, Checkbox, message, Spin } from 'antd';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash';
import Checklist from '../task-components/checklist';
import CheckItemInfo from '../task-components/checklist/check-item/info';
import CheckItemResult from '../task-components/checklist/check-item/result';
import useChecklistData from '../task-components/checklist/hooks/use-checklist-data';
import useChecklistOperate from '../task-components/checklist/hooks/use-checklist-operate';
import ItemWrapper from '../task-components/checklist/item-wrapper';
import ReviewItem from '../task-components/checklist/review-item';
import ReviewItemResult from '../task-components/checklist/review-item/result';
import usePreviewModal from '../task-components/hooks/use-preview-modal';
import TaskInfo from '../task-components/info';
import TaskInfoBody from '../task-components/info/body';
import TaskPage from '../task-components/page';
import SumUp from '../task-components/sum-up';
import SumUpResult from '../task-components/sum-up/result';
import { TaskEventEmitter, TaskNotice } from '../task-components/utils/emit';
import {
  filterReviewNotReview,
  filterReviewNotReviewAndUnqualified,
  filterUnqualified,
} from '../task-components/utils/filter';
import { verifyAllIsApply, verifyCheckItem, verifyOverdue, verifyReviewResult } from '../task-components/utils/verify';
import Notice from '@/assets/images/notice2.svg';
import { NotFilledItemHandleType, StrategyTaskType } from '@/constants/strategy';
import useTaskDetail from '@/hooks/use-task-detail';
// import { saveReviewItem as saveItem } from '@/http/apis/report-center';

export interface TaskReviewProps {
  taskId: string | number;
  needReviewSummary?: boolean;
  hasReview?: boolean;
  saveReviewItem?: (dto: any) => Promise<any>;
  onPreview?: ({
    notFilledItemHandleType,
    reviewSumUp,
  }: {
    notFilledItemHandleType?: NotFilledItemHandleType;
    reviewSumUp: string;
  }) => void;
}

const TaskReview: FC<TaskReviewProps> = ({ taskId, needReviewSummary, hasReview, onPreview, saveReviewItem }) => {
  const { detail } = useTaskDetail({ taskId, needReviewSummary, hasReview });
  const [showUnqualified, setShowUnqualified] = useState<boolean>(false);
  const [showNotReview, setShowNotReview] = useState<boolean>(false);
  const [worksheets, setWorksheets] = useState([]);

  const filterFn = useCallback(() => {
    if (showNotReview && showUnqualified) {
      return filterReviewNotReviewAndUnqualified;
    }

    if (showUnqualified) {
      return filterUnqualified;
    }

    if (showNotReview) {
      return (item: any) => filterReviewNotReview(item, detail?.info?.taskStatus);
    }

    return undefined;
  }, [detail?.info?.taskStatus, showNotReview, showUnqualified]);
  const { worksheetMap, worksheetOptions, worksheetCategoryMap } = useChecklistData({
    worksheets,
    filter: filterFn(),
    progressCheck: verifyCheckItem,
  });
  const { activeWorksheetId, setActiveWorksheetId, activeCategoryId, setActiveCategoryId, switchCategory } =
    useChecklistOperate({ worksheetMap, worksheetOptions });
  const [sumUpValue, setSumUpValue] = useState<string>();

  const { showModal } = usePreviewModal();

  useEffect(() => {
    if (detail?.worksheets) {
      setWorksheets(detail?.worksheets);
    }
  }, [detail?.worksheets]);

  const updateLocalData = useCallback(
    (data) => {
      const { dto, worksheetItem } = data;
      const { worksheetIndex, worksheetCategoryIndex, worksheetItemIndex } = worksheetItem;
      const worksheetsClone = cloneDeep(worksheets);

      worksheetsClone[worksheetIndex].data[worksheetCategoryIndex].data[worksheetItemIndex].reviewResult = {
        ...dto,
      };

      setWorksheets(worksheetsClone);
    },
    [worksheets],
  );

  useEffect(() => {
    const saveItem = (data: any) => {
      const { dto } = data;

      saveReviewItem(dto).then(() => {
        updateLocalData(data);
      });
    };

    TaskEventEmitter.addListener(TaskNotice, saveItem);

    return () => {
      TaskEventEmitter.removeListener(TaskNotice, saveItem);
    };
  }, [updateLocalData, saveReviewItem]);

  return (
    <TaskPage className="overflow-auto">
      {detail?.loading ? (
        <div className="h-full flex justify-center items-center">
          <Spin tip="加载中......" size="large">
            <div className="w-[200px]" />
          </Spin>
        </div>
      ) : (
        <>
          <TaskInfo data={detail?.info}>
            <TaskInfoBody data={detail?.info} />
          </TaskInfo>
          <Checklist
            extra={
              <div>
                {detail?.info?.taskType === StrategyTaskType.PATROL && (
                  <Checkbox
                    checked={showUnqualified}
                    onChange={(e) => {
                      setShowUnqualified(e.target.checked);
                    }}
                  >
                    仅显示不合格项
                  </Checkbox>
                )}
                <Checkbox
                  checked={showNotReview}
                  onChange={(e) => {
                    setShowNotReview(e.target.checked);
                  }}
                >
                  未点评项
                </Checkbox>
              </div>
            }
            worksheetOptions={worksheetOptions}
            categoryOptions={activeWorksheetId && worksheetMap?.[activeWorksheetId]?.categoryOptions}
            categoryPanels={activeCategoryId && worksheetCategoryMap?.[activeCategoryId]?.categorys}
            renderCollapseExtra={(item) => {
              return `${item?.progress}/${item?.itemOptions?.length}`;
            }}
            onWorksheetChange={(val) => {
              setActiveWorksheetId(val);

              switchCategory(val);
            }}
            renderWorksheetExtra={() => {
              return worksheetMap?.[activeWorksheetId]?.reviewExpiredTime ? (
                <div className="flex h-7 flex-row items-center gap-x-1 bg-[#FFF9ED] px-4">
                  <img src={Notice} width={16} height={16} alt="" />
                  {/* <Notice width={16} height={16} color="#FF7D00" /> */}
                  <div className="text-xs text-[#FF7D00]">
                    须{dayjs(worksheetMap?.[activeWorksheetId]?.reviewExpiredTime).format('MM月DD号 HH:mm')}
                    前完成点评
                  </div>
                </div>
              ) : undefined;
            }}
            renderCategoryLabel={(item) => {
              return `${item?.name} (${item?.progress}/${item?.itemOptions?.length})`;
            }}
            onCategoryChange={(val) => {
              setActiveCategoryId(val);
            }}
            renderCheckItem={(item) => {
              return item?.hidden ? undefined : (
                <ItemWrapper value={JSON.stringify({ ...item, taskStatus: detail?.info?.taskStatus })}>
                  <CheckItemInfo />
                  <CheckItemResult />
                  <ReviewItemResult />
                  <ReviewItem />
                </ItemWrapper>
              );
            }}
          />
          {detail?.info?.summary && <SumUpResult label="巡检总结" value={detail?.info?.summary} />}
          {needReviewSummary &&
            detail?.reviewSummarys &&
            Object.keys(detail?.reviewSummarys)?.length > 0 &&
            Object.keys(detail?.reviewSummarys)?.map((key: string) => {
              const content: string = detail?.reviewSummarys?.[key]
                ?.map(({ username, roleNames, summary }: any) => `${username}(${roleNames?.join('、')})：${summary}`)
                .join('\n');

              return <SumUpResult value={content} label={`点评总结${key}`} />;
            })}
          <SumUp
            placeholder="请输入点评总结"
            value={sumUpValue}
            onChange={(e) => {
              setSumUpValue(e.target.value);
            }}
          />
          <div className="p-3 bg-white">
            {worksheetMap?.[activeWorksheetId]?.reviewExpiredTime &&
            verifyOverdue(worksheetMap?.[activeWorksheetId]?.reviewExpiredTime) ? (
              <Button disabled={true} type="primary" className="w-full">
                未按时完成，点评已关闭
              </Button>
            ) : (
              <Button
                type="primary"
                className="w-full"
                onClick={() => {
                  const { notReviewedNecessaryCount, notReviewedOptionalCount } = verifyReviewResult(worksheets);

                  if (notReviewedNecessaryCount > 0) {
                    message.error(`还有${notReviewedNecessaryCount}项必填项未点评，请确认后再提交点评`);

                    return;
                  }

                  if (notReviewedOptionalCount > 0) {
                    showModal({
                      title: '预览报告失败',
                      content: `有${notReviewedOptionalCount}检查项未点评，请选择处理方案`,
                      onOk: (value) => {
                        onPreview?.({
                          reviewSumUp: sumUpValue,
                          notFilledItemHandleType: value,
                        });
                      },
                      validate: () => {
                        if (verifyAllIsApply(worksheets, true)) {
                          message.error('不能所有项都是“不适用”，请返回修改');

                          return Promise.resolve(false);
                        }

                        return Promise.resolve(true);
                      },
                    });

                    return;
                  }

                  onPreview?.({
                    reviewSumUp: sumUpValue,
                  });
                }}
              >
                预览报告
              </Button>
            )}
          </div>
        </>
      )}
    </TaskPage>
  );
};

export default TaskReview;
