import { createContext, useContext } from 'react';
import { DrawerProps } from 'antd';
import { IReportAppealInfo } from '@/http/apis/appeal';

export type CheckItemProps = {
  drawerPlacement?: DrawerProps['placement'];
  baseTaskId?: number | string;
  taskSubType?: string; // 巡检子类型
  reviewTaskId?: string; // 点评任务id
  remark?: string;
  id?: string; // 任务检查项id
  taskStatus?: string; // 任务状态
  checkedValidity?: boolean; // 效期是否使用checkbox 选择未售或缺货
  disabled?: boolean; // 是否支持编辑
  taskId?: string; // 任务ID
  accentedTermTags: string[]; // 检查项属性
  itemType: string; // 检查项类型
  worksheetItemContent: string; // 检查项内容
  sopId?: string; // SOPID
  worksheetIndex?: number; // 检查表索引
  worksheetCategoryIndex?: number; // 检查项分类索引
  worksheetItemIndex?: number; // 检查项索引
  taskWorksheetItemId?: number;
  taskItemId?: number;
  simpleImages?: any;
  modalWidth?: number;
  worksheetItem?: {
    type?: string; // 巡检类型
    scoringStandard?: string; // 评分标准
    actionType?: string; // 检查项类型
    normalButtonName?: string; // 自检合格按钮名称
    unqualifiedButtonName?: string; // 自检不合格按钮名称
    afterActionType?: string; // 后续动作
    afterActionMethod?: string; // 后续动作
    afterActionRelationSopId?: string; // 后续动作关联sop
    afterActionPhotoNormalMark?: string; // 后续动作正常结果备注
    afterActionPhotoUnqualifiedMark?: string; // 后续动作非正常结果备注
    afterActionPhotoMark?: boolean;
    afterActionInputBoxRemainderCopy?: string;
    unqualifiedMustSelectReason?: boolean;
    unqualifiedReasons?: string[];
    // 巡检
    displayScore?: any; // 是否显示分数
    scoreMax?: number; // 打分最大值
    unqualifiedMustUpload?: boolean; // 不合格是否必传图片
    normalMustUpload?: boolean; // 合格是否必传图片
    uploadImageMethod?: any; // 是否允许使用相册上传
  };
  differentItemLabel?: string;
  validityDetails?: any[];
  data?: {
    // 自检、巡检任务结果
    itemImages?: any[];
    itemRemark?: string;
    itemJudge?: boolean;
    itemReason?: string[];
    itemOtherReason?: string;
    itemScore?: string;
    hasApply?: boolean;
    validityStatus?: string; // 效期状态
    issueConfigDTO?: any;
    issueAtOnceRemark?: string;
    issueAtOnceImages?: any[];
    reformLimit?: any; // 整改期限
    qualifiedItemImages?: any[]; // 不合格项图片
    qualifiedRemark?: string; // 不合格项备注

    taskId?: number;
    baseTaskId?: number;
    taskItemId?: number;
  };
  issueItemSnap?: any;
  reviewItemSnap?: any;
  result?: any; // 点评结果列表
  /** 抄送人列表选项 */
  copyUserOptions?: { label: string; value: number }[];
  /** 当前报告是否需要提交整改说明 */
  hasNeedCommitIssueInstructions?: boolean;
  reviewResult?: any;
  copyUsers?: any[];
  onSave?: (data: any) => void;
  newWindow?: boolean;
  systemConfig?: any; // 系统默认配置
  modalStyle?: any;
  reviewType?: string;
  reportAppealInfoData?: IReportAppealInfo;
};

export const CheckItemContext = createContext<CheckItemProps>({
  accentedTermTags: [],
  itemType: '',
  worksheetItemContent: '',
});

export interface CheckItemContextProviderProps {
  value: CheckItemProps;
  children: React.ReactNode;
}

// eslint-disable-next-line react-refresh/only-export-components
export const useCheckItemContext = () => useContext(CheckItemContext);

const CheckItemContextProvider = ({ value, children }: CheckItemContextProviderProps) => {
  return <CheckItemContext.Provider value={value}>{children}</CheckItemContext.Provider>;
};

export default CheckItemContextProvider;
