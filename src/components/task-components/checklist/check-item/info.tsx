import { FC, ReactNode, useMemo, useState } from 'react';
import { useRequest } from 'ahooks';
import { Button, Drawer, Empty, Image, message, Typography } from 'antd';
import { useCheckItemContext } from '../context/CheckItemContext';
import MediaCard from '@/components/MediaCard';
import { CheckItemAttributeTag } from '@/constants/checklist';
import { getAppealProcess } from '@/http/apis/appeal';
import { AppealStatus } from '@/pages/task/complaints/const';
import { InfoCard } from '@/pages/taskCenter/foodAppeal/components/ComplaintDescCard';
import { OprAction, RecordModal } from '@/pages/taskCenter/foodAppeal/components/RecordModal';
import { querySopDetail } from '@/services/sop';
import { windowOpen } from '@/utils/jump';

export interface CheckItemInfoProps {}

const CheckItemInfo: FC<CheckItemInfoProps> = () => {
  const {
    itemType,
    worksheetItem,
    accentedTermTags,
    sopId,
    worksheetItemContent,
    simpleImages,
    drawerPlacement,
    newWindow,
    differentItemLabel,
    reportAppealInfoData,
    data,
  } = useCheckItemContext();

  const currentAppealItem = useMemo(() => {
    return reportAppealInfoData?.reportAppealItems?.find((f) => f.taskItemId === data?.taskItemId);
  }, [data?.taskItemId, reportAppealInfoData?.reportAppealItems]);

  const { run: getAppealProcessRun, loading: getAppealProcessLoading } = useRequest(
    () => {
      const _payload = {
        baseTaskId: reportAppealInfoData?.baseTaskId,
        taskId: currentAppealItem?.taskId,
        taskItemId: currentAppealItem?.taskItemId,
      };

      return getAppealProcess(_payload);
    },
    {
      manual: true,
      onSuccess: (data: any) => {
        if (data?.length) {
          RecordModal.showModal({
            data: {
              record: data?.map((d) => ({
                operatorName: d?.operatorUserName,
                updateTime: d?.createTime,
                operateAction: d?.operationType,
                extra: (
                  <InfoCard
                    remark={d?.remark}
                    attachments={d?.attachments}
                    remarkLabel={d?.operationType === OprAction.审核驳回 ? '驳回原因' : undefined}
                  />
                ),
              })),
            },
          });
        } else {
          message.error('暂无申诉记录');
        }
      },
    },
  );

  const renderAppealRecordInfo = ({ appealStatus }: { appealStatus: AppealStatus }) => {
    if (
      [
        AppealStatus.发起,
        AppealStatus.通过,
        AppealStatus.驳回,
        AppealStatus.撤回,
        AppealStatus.已过期,
        AppealStatus.审核超时转派,
        AppealStatus.已作废,
        AppealStatus.已提交,
      ].includes(appealStatus as any)
    ) {
      return (
        <Button type="link" onClick={getAppealProcessRun} loading={getAppealProcessLoading}>
          申诉记录
        </Button>
      );
    }

    return null;
  };

  const [drawerProps, setDrawerProps] = useState<{ open: boolean; title?: string; content?: ReactNode }>({
    open: false,
  });

  const simpleImageList = useMemo(() => {
    return simpleImages?.map((v) => {
      return {
        ...v,
        contentType: 'IMAGE',
        name: '示例图片',
        id: v?.fileObject?.key,
      };
    });
  }, [simpleImages]);

  return itemType !== 'VALIDITY' ? (
    <>
      {/* 左边是重点项，右边是SOP、参考标准等 */}
      <div className="flex flex-row justify-between">
        <div className="flex flex-row flex-wrap gap-1">
          {accentedTermTags?.map((key: any) => CheckItemAttributeTag[key])}
        </div>
        <div className="flex flex-row shrink-0">
          {sopId ? (
            <a
              className="text-sm text-primary cursor-pointer "
              onClick={async () => {
                const res: any = await querySopDetail(+sopId);

                if (res?.content?.fileType === 'PDF') {
                  return windowOpen(`${import.meta.env.VITE_BASE_URL}pdf.html?pdfUrl=${btoa(res?.content?.fileUrl)}`);
                }

                setDrawerProps({
                  open: true,
                  title: '参考标准',
                  content: (
                    <div>
                      <Typography.Title level={5}>SOP</Typography.Title>
                      {res?.hasUsePermission ? (
                        res?.content?.fileType === 'VIDEO' ? (
                          <div>
                            <video
                              key="Player"
                              // className={styles.video}
                              id="myPlayer"
                              controls
                              playsInline
                              webkit-playsinline
                            >
                              <source src={res?.content?.fileUrl} />
                            </video>
                          </div>
                        ) : (
                          <div>
                            {res?.content?.imageUrls?.map((item, index) => {
                              return (
                                <Image
                                  style={{ cursor: 'pointer' }}
                                  preview={!newWindow}
                                  onClick={() => {
                                    if (newWindow) {
                                      windowOpen(item, '_blank');
                                    }
                                  }}
                                  key={index}
                                  src={item}
                                />
                              );
                            })}
                          </div>
                        )
                      ) : (
                        <Empty description="暂无权限" />
                      )}
                    </div>
                  ),
                });
              }}
            >
              参考标准
            </a>
          ) : null}
          {worksheetItem?.scoringStandard ? (
            <a
              className="ml-2 text-sm text-primary cursor-pointer"
              onClick={() => {
                setDrawerProps({
                  open: true,
                  title: '评分标准',
                  content: worksheetItem?.scoringStandard,
                });
              }}
            >
              评分标准
            </a>
          ) : null}
        </div>
      </div>
      {/* 检查项内容 */}
      <div className="my-2.5 flex flex-row items-center justify-between">
        <span className="text-sm font-medium leading-[22px] text-[#141414]">{worksheetItemContent}</span>
        {renderAppealRecordInfo({ appealStatus: currentAppealItem?.appealStatus })}
      </div>
      {/* 示例图 */}
      {simpleImageList?.length > 0 && (
        <div className="flex flex-wrap gap-2 mb-2">
          {simpleImageList?.map((item) => (
            <MediaCard
              file={item}
              isSampleImg
              key={item.id}
              newWindow={newWindow}
              fileList={simpleImageList}
              width={50}
              height={50}
            />
          ))}
        </div>
      )}
      <Drawer
        open={drawerProps?.open}
        title={drawerProps?.title}
        placement={drawerPlacement}
        width={400}
        onClose={() => {
          setDrawerProps({ open: false });
        }}
      >
        {drawerProps?.content}
      </Drawer>
      <RecordModal />
    </>
  ) : null;
};

export default CheckItemInfo;
