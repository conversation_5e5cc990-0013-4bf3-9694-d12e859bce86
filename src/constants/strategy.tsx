import { isBoolean } from 'lodash';
import { RouteKey } from '@/router/config';

export enum StrategyTaskType {
  SELF = 'SELF',
  PATROL = 'PATROL',
  VALIDITY = 'VALIDITY',
  ISSUE = 'ISSUE',
}

export const StrategyTaskTypeCN: Record<StrategyTaskType, string> = {
  [StrategyTaskType.SELF]: '自检任务',
  [StrategyTaskType.PATROL]: '巡检任务',
  [StrategyTaskType.VALIDITY]: '效期任务',
  [StrategyTaskType.ISSUE]: '整改任务',
};

export enum StrategyPatrolType {
  /** 到店巡检 */
  NORMAL = 'NORMAL',
  /** 视频云巡检 */
  VIDEO = 'VIDEO',
  /** 食安线下稽核 */
  FOOD_SAFETY_NORMAL = 'FOOD_SAFETY_NORMAL',
  /** 食安线上稽核 */
  FOOD_SAFETY_VIDEO = 'FOOD_SAFETY_VIDEO',
  /** 诊断巡检 */
  DIAGNOSTIC = 'DIAGNOSTIC',
  /** 食安稽核到店辅导 */
  FOOD_SAFETY_ARRIVE_SHOP = 'FOOD_SAFETY_ARRIVE_SHOP',
  /** 差异项到店任务 */
  DIFFERENCE_ITEM_ARRIVE_SHOP = 'DIFFERENCE_ITEM_ARRIVE_SHOP',
  /** 交接任务 */
  HANDOVER = 'HANDOVER',
}

export const StrategyPatrolTypeCN: Record<StrategyPatrolType, string> = {
  [StrategyPatrolType.NORMAL]: '到店巡检',
  [StrategyPatrolType.VIDEO]: '视频云巡检',
  [StrategyPatrolType.FOOD_SAFETY_NORMAL]: '食安线下稽核',
  [StrategyPatrolType.FOOD_SAFETY_VIDEO]: '食安线上稽核',
  [StrategyPatrolType.DIAGNOSTIC]: '诊断任务',
  [StrategyPatrolType.FOOD_SAFETY_ARRIVE_SHOP]: '食安稽核到店辅导',
  [StrategyPatrolType.DIFFERENCE_ITEM_ARRIVE_SHOP]: '差异项到店任务',
  [StrategyPatrolType.HANDOVER]: '交接任务',
};

export enum StrategyTaskKind {
  BASE = 'BASE',
  REVIEW = 'REVIEW',
  ISSUE = 'ISSUE',
}

export enum StrategyCycleType {
  DAILY = 'DAILY',
  WEEKLY = 'WEEKLY',
  MONTHLY = 'MONTHLY',
  QUARTERLY = 'QUARTERLY',
}

export const StrategyCycleTypeCN: Record<StrategyCycleType, string> = {
  [StrategyCycleType.DAILY]: '日',
  [StrategyCycleType.WEEKLY]: '周',
  [StrategyCycleType.MONTHLY]: '月',
  [StrategyCycleType.QUARTERLY]: '季',
};

export const StrategyTaskKindCN: Record<StrategyTaskKind, string> = {
  [StrategyTaskKind.BASE]: '基础任务',
  [StrategyTaskKind.ISSUE]: '整改任务',
  [StrategyTaskKind.REVIEW]: '点评任务',
};

export const StrategyTaskTypeMap: any = {
  [StrategyTaskType.SELF]: 'BASE_SELF',
  [StrategyTaskType.PATROL]: 'BASE_PATROL',
  [StrategyTaskKind.REVIEW]: 'EXTEND_REVIEW',
  [StrategyTaskKind.ISSUE]: 'EXTEND_ISSUE',
};

export enum StrategyTemplateType {
  SELF = 'SELF',
  NORMAL = 'PATROL',
  REVIEW = 'REVIEW',
  ISSUE = 'ISSUE',
  DIAGNOSTIC = 'DIAGNOSTIC',
}

export const StrategyTemplateTypeCN: any = {
  [StrategyTemplateType.SELF]: '自检任务',
  [StrategyTemplateType.NORMAL]: '巡检任务',
  [StrategyTemplateType.REVIEW]: '点评任务',
  [StrategyTemplateType.ISSUE]: '整改任务',
  [StrategyTemplateType.DIAGNOSTIC]: '诊断任务',
};

export const StrategyTemplateRouterDetail = {
  // 自检任务模板-模板详情
  [StrategyTemplateType.SELF]: RouteKey.TSDetail,
  // 巡检任务模板-模板详情
  [StrategyTemplateType.NORMAL]: RouteKey.TRDetail,
  [StrategyTemplateType.REVIEW]: RouteKey.TRVDetail,
  [StrategyTemplateType.ISSUE]: RouteKey.TRFDetail,
};

export enum TaskSubType {
  SELF = 'SELF',
  PATROL = 'PATROL',
  REVIEW = 'REVIEW',
  ISSUE = 'ISSUE',
}
export const TaskSubTypeCN: any = {
  [TaskSubType.SELF]: '自检任务',
  [TaskSubType.PATROL]: '巡检任务',
  [TaskSubType.REVIEW]: '点评任务',
  [TaskSubType.ISSUE]: '整改任务',
};

export enum StrategyType {
  FOODSAFETY = 'FOODSAFETY',
  NONFOODSAFETY = 'NONFOODSAFETY',
}

export const StrategyTypeCN: Record<StrategyType, string> = {
  [StrategyType.FOODSAFETY]: '食安',
  [StrategyType.NONFOODSAFETY]: '非食安',
};

export enum StrategyRoutineType {
  NORMAL = 'NORMAL',
  VIDEO = 'VIDEO',
  FOOD_SAFETY_NORMAL = 'FOOD_SAFETY_NORMAL',
  FOOD_SAFETY_VIDEO = 'FOOD_SAFETY_VIDEO',
  FOOD_SAFETY_ARRIVE_SHOP = 'FOOD_SAFETY_ARRIVE_SHOP',
  DIAGNOSTIC = 'DIAGNOSTIC',
  DIFFERENCE_ITEM_ARRIVE_SHOP = 'DIFFERENCE_ITEM_ARRIVE_SHOP',
  HANDOVER = 'HANDOVER',
}

export const StrategyRoutineTypeCN: Record<StrategyRoutineType, string> = {
  [StrategyRoutineType.NORMAL]: '到店巡检',
  [StrategyRoutineType.VIDEO]: '视频云巡检',
  [StrategyRoutineType.FOOD_SAFETY_NORMAL]: '食安线下稽核',
  [StrategyRoutineType.FOOD_SAFETY_VIDEO]: '食安线上稽核',
  [StrategyRoutineType.FOOD_SAFETY_ARRIVE_SHOP]: '食安稽核到店辅导',
  [StrategyRoutineType.DIAGNOSTIC]: '诊断任务',
  [StrategyRoutineType.DIFFERENCE_ITEM_ARRIVE_SHOP]: '差异项到店任务',
  [StrategyRoutineType.HANDOVER]: '交接任务',
};

export enum StrategyTaskStatus {
  WAITING_CONFIRM_ARRIVE = 'WAITING_CONFIRM_ARRIVE',
  WAITING_START = 'WAITING_START',
  RUNNING = 'RUNNING',
  AUDITING = 'AUDITING',
  TRANSFER_AUDIT = 'TRANSFER_AUDIT',
  COMPLETED = 'COMPLETED',
  CANCELED = 'CANCELED',
  AUDIT_EXPIRED = 'AUDIT_EXPIRED',
  EXPIRED = 'EXPIRED',
  EXPIRED_RUNNING = 'EXPIRED_RUNNING',
}

export const StrategyTaskStatusCN: Record<StrategyTaskStatus, string> = {
  [StrategyTaskStatus.WAITING_CONFIRM_ARRIVE]: '执行时段确认中',
  [StrategyTaskStatus.WAITING_START]: '待开始',
  [StrategyTaskStatus.RUNNING]: '进行中',
  [StrategyTaskStatus.AUDITING]: '待确认',
  [StrategyTaskStatus.TRANSFER_AUDIT]: '任务转办审核中',
  [StrategyTaskStatus.COMPLETED]: '已完成',
  [StrategyTaskStatus.CANCELED]: '已作废',
  [StrategyTaskStatus.AUDIT_EXPIRED]: '审核逾期',
  [StrategyTaskStatus.EXPIRED]: '已逾期',
  [StrategyTaskStatus.EXPIRED_RUNNING]: '逾期进行中',
};

export enum CheckItemTypeEnum {
  SELF = 1,
  VALIDITY = 1 << 1,
  PATROL = 1 << 2,
}

export enum CheckItemActionTypeEnum {
  PHOTOGRAPH = 1 << 10,
  JUDGE = 1 << 11,
  GAP_FILLING = 1 << 12,
  SCORE = 1 << 13,
}

export const CheckItemProgressCheckFn = {
  [CheckItemTypeEnum.SELF | CheckItemActionTypeEnum.PHOTOGRAPH]: (data: any) => {
    const { data: taskResult } = data;

    if (taskResult?.itemImages?.length > 0) {
      return true;
    }

    return false;
  },
  [CheckItemTypeEnum.SELF | CheckItemActionTypeEnum.JUDGE]: (data: any) => {
    const { data: taskResult } = data;

    if (isBoolean(taskResult?.itemJudge)) {
      return true;
    }

    return false;
  },
  [CheckItemTypeEnum.SELF | CheckItemActionTypeEnum.GAP_FILLING]: (data: any) => {
    const { data: taskResult } = data;

    if (taskResult?.itemRemark) {
      return true;
    }

    return false;
  },
  [CheckItemTypeEnum.VALIDITY]: (data: any) => {
    if (data?.validityDetails?.length > 0) {
      return true;
    }

    return false;
  },
  [CheckItemTypeEnum.PATROL | CheckItemActionTypeEnum.JUDGE]: (data: any) => {
    const { data: taskResult } = data;

    if (isBoolean(taskResult?.itemJudge) || (isBoolean(taskResult?.hasApply) && !taskResult?.hasApply)) {
      return true;
    }

    return false;
  },
  [CheckItemTypeEnum.PATROL | CheckItemActionTypeEnum.SCORE]: (data: any) => {
    const { data: taskResult } = data;

    if (isBoolean(taskResult?.itemJudge) || (isBoolean(taskResult?.hasApply) && !taskResult?.hasApply)) {
      return true;
    }

    return false;
  },
};

export enum SelfCheckItemActionType {
  // 填空
  GAP_FILLING = 'GAP_FILLING',
  // 拍照
  PHOTOGRAPH = 'PHOTOGRAPH',
  // 判断
  JUDGE = 'JUDGE',
}

export enum SelfCheckItemAfterActionMethod {
  // 都拍照
  ALL = 'ALL',
  // 正常结果拍照
  WHEN_NORMAL = 'WHEN_NORMAL',
  // 非正常结果拍照
  WHEN_ABNORMAL = 'WHEN_ABNORMAL',
}

export enum PatrolCheckItemType {
  // 打分型
  SCORE = 'SCORE',
  // 判断
  JUDGE = 'JUDGE',
}

export enum StrategyReviewType {
  // 人工判断型
  MANUAL_JUDGMENT = 'MANUAL_JUDGMENT',
  // 人工打分型
  MANUAL_SCORING = 'MANUAL_SCORING',
  // 无需点评
  NO_REVIEWS_NEEDED = 'NO_REVIEWS_NEEDED',
}

export enum NotFilledItemHandleType {
  SET_FULL_SCORE = 'SET_FULL_SCORE',
  SET_NOT_APPLY = 'SET_NOT_APPLY',
}

export enum StrategyTaskReportStatus {
  WAITING_SUBMIT = 'WAITING_SUBMIT',
  WAITING_CONFIRM = 'WAITING_CONFIRM',
  CONFIRMED = 'CONFIRMED',
  CANCELED = 'CANCELED',
}

export const StrategyTaskReportStatusCN: Record<StrategyTaskReportStatus, string> = {
  [StrategyTaskReportStatus.WAITING_SUBMIT]: '待提交',
  [StrategyTaskReportStatus.WAITING_CONFIRM]: '待确认',
  [StrategyTaskReportStatus.CONFIRMED]: '已确认',
  [StrategyTaskReportStatus.CANCELED]: '已作废',
};

export enum StrategyRectificationMethod {
  SHOP_ISSUE = 'SHOP_ISSUE',
  AT_ONCE_ISSUE = 'AT_ONCE_ISSUE',
  SELECT_ISSUE = 'SELECT_ISSUE',
}

export const StrategyRectificationMethodCN: Record<StrategyRectificationMethod, string> = {
  [StrategyRectificationMethod.SHOP_ISSUE]: '门店整改',
  [StrategyRectificationMethod.AT_ONCE_ISSUE]: '当场整改',
  [StrategyRectificationMethod.SELECT_ISSUE]: '巡检人选择整改方式',
};

export enum StrategyStudyStatus {
  NO_STUDY = 'NO_STUDY',
  WAITING_COMPLETE = 'WAITING_COMPLETE',
  COMPLETED = 'COMPLETED',
}

export const StrategyStudyStatusCN: Record<StrategyStudyStatus, string> = {
  [StrategyStudyStatus.NO_STUDY]: '无需学习',
  [StrategyStudyStatus.WAITING_COMPLETE]: ' 未学习',
  [StrategyStudyStatus.COMPLETED]: '已学习',
};
export enum StrategyIssueType {
  SHOP_ISSUE = 'SHOP_ISSUE',
  AT_ONCE_ISSUE = 'AT_ONCE_ISSUE',
}
export const StrategyIssueTypeCN: Record<StrategyIssueType, string> = {
  [StrategyIssueType.SHOP_ISSUE]: '门店整改',
  [StrategyIssueType.AT_ONCE_ISSUE]: ' 当场整改',
};
export enum StrategyOperationAction {
  CREATED = 'CREATED',
  CANCELED = 'CANCELED',
  SUBMITTED = 'SUBMITTED',
  REVOCATION = 'REVOCATION',
  REVIEWS = 'REVIEWS',
  REVOCATION_REVIEWS = 'REVOCATION_REVIEWS',
  CONFIRMED = 'CONFIRMED',
  REVOCATION_WORKSHEET = 'REVOCATION_WORKSHEET',
  FEED_BACK = 'FEED_BACK',
  REJECTED = 'REJECTED',
  AUDITED = 'AUDITED',
  EXPIRED = 'EXPIRED',

  CONFIRM_EXE_PERIOD = 'CONFIRM_EXE_PERIOD',
  FOOD_SAFETY_ARRIVE_SHOP_CONFIRM_EXAM_PASS = 'FOOD_SAFETY_ARRIVE_SHOP_CONFIRM_EXAM_PASS',
  FOOD_SAFETY_ARRIVE_SHOP_CANCEL_CONFIRM_EXAM_PASS = 'FOOD_SAFETY_ARRIVE_SHOP_CANCEL_CONFIRM_EXAM_PASS',
  CONFIRM_ARRIVE = 'CONFIRM_ARRIVE',
  TRANSFER_REJECT = 'TRANSFER_REJECT',
  TRANSFER_CANCEL = 'TRANSFER_CANCEL',
  TRANSFER_APPROVED = 'TRANSFER_APPROVED',
  TRANSFER_OVERDUE = 'TRANSFER_OVERDUE',
  TRANSFER_ACCEPTED = 'TRANSFER_ACCEPTED',
  TRANSFER_REJECT_ACCEPT = 'TRANSFER_REJECT_ACCEPT',

  RECTIFICATION_OVERDUE = 'RECTIFICATION_OVERDUE',
  AUDITED_OVERDUE = 'AUDITED_OVERDUE',
}

export const StrategyOperationActionCN: Record<StrategyOperationAction, string> = {
  [StrategyOperationAction.CREATED]: '创建',
  [StrategyOperationAction.CANCELED]: '作废',
  [StrategyOperationAction.SUBMITTED]: '提交',
  [StrategyOperationAction.REVOCATION]: '撤回',
  [StrategyOperationAction.REVIEWS]: '点评',
  [StrategyOperationAction.REVOCATION_REVIEWS]: '撤回点评',
  [StrategyOperationAction.CONFIRMED]: '确认报告',
  [StrategyOperationAction.REVOCATION_WORKSHEET]: '撤回自检检查表',
  [StrategyOperationAction.FEED_BACK]: '提交反馈',
  [StrategyOperationAction.REJECTED]: '驳回',
  [StrategyOperationAction.AUDITED]: '审核通过',
  [StrategyOperationAction.EXPIRED]: '任务超时',

  [StrategyOperationAction.CONFIRM_EXE_PERIOD]: '执行时段确认',
  [StrategyOperationAction.FOOD_SAFETY_ARRIVE_SHOP_CONFIRM_EXAM_PASS]: '确认考试通过',
  [StrategyOperationAction.FOOD_SAFETY_ARRIVE_SHOP_CANCEL_CONFIRM_EXAM_PASS]: '取消确认考试通过',
  [StrategyOperationAction.CONFIRM_ARRIVE]: '确认按时到店',
  [StrategyOperationAction.TRANSFER_REJECT]: '驳回转办',
  [StrategyOperationAction.TRANSFER_CANCEL]: '转办取消',
  [StrategyOperationAction.TRANSFER_APPROVED]: '转办审核通过',
  [StrategyOperationAction.TRANSFER_OVERDUE]: '工单超时流转',
  [StrategyOperationAction.TRANSFER_ACCEPTED]: '权限外转办接受',
  [StrategyOperationAction.TRANSFER_REJECT_ACCEPT]: '权限外转办拒绝接受',

  [StrategyOperationAction.RECTIFICATION_OVERDUE]: '整改逾期',
  [StrategyOperationAction.AUDITED_OVERDUE]: '审核逾期',
};

export enum StrategyTutorTaskType {
  ABNORMAL = 'ABNORMAL',
  RECHECK = 'RECHECK',
}

export const StrategyTutorTaskTypeCN: Record<StrategyTutorTaskType, string> = {
  [StrategyTutorTaskType.ABNORMAL]: '不合格门店',
  [StrategyTutorTaskType.RECHECK]: '复检门店',
};

export enum StrategyRectificationLoopType {
  ONCE_OF_DAY = 'ONCE_OF_DAY',
  ONCE_OF_WEEK = 'ONCE_OF_WEEK',
}

export const StrategyRectificationLoopTypeCN: Record<StrategyRectificationLoopType, string> = {
  [StrategyRectificationLoopType.ONCE_OF_DAY]: '每日一次',
  [StrategyRectificationLoopType.ONCE_OF_WEEK]: '每周一次',
};

export enum StrategyRectificationIssueType {
  SELECT_SHOP_ISSUE = 'SELECT_SHOP_ISSUE',
  SELECT_AT_ONCE_ISSUE = 'SELECT_AT_ONCE_ISSUE',
}

export const StrategyRectificationIssueTypeCN: Record<StrategyRectificationIssueType, string> = {
  [StrategyRectificationIssueType.SELECT_SHOP_ISSUE]: '门店整改',
  [StrategyRectificationIssueType.SELECT_AT_ONCE_ISSUE]: '当场整改',
};

export enum StrategyVideoPatrolType {
  ONLINE_SPECIALIST = 'ONLINE_SPECIALIST',
  ONLINE_SUPERVISE = 'ONLINE_SUPERVISE',
  // ONLINE_SAFETY_VIDEO = 'ONLINE_SAFETY_VIDEO',
}

export const StrategyVideoPatrolTypeCN: Record<StrategyVideoPatrolType, string> = {
  [StrategyVideoPatrolType.ONLINE_SPECIALIST]: '线上专员巡检专项任务',
  [StrategyVideoPatrolType.ONLINE_SUPERVISE]: '线上专员到店人员监管任务',
  // [StrategyVideoPatrolType.ONLINE_SAFETY_VIDEO]: '食安线上稽核',
};

export enum StrategySpecialistRiskLevel {
  BRONZE_SILVER = 'BRONZE_SILVER',
  FOOD_SAFETY_FOCUS = 'FOOD_SAFETY_FOCUS',
  PRODUCT_UNDERCOOKED = 'PRODUCT_UNDERCOOKED',
  SHOP_STORE = 'SHOP_STORE',
  PLATINUM_HEAVENLY_CHAMPION = 'PLATINUM_HEAVENLY_CHAMPION',
  SPECIAL = 'SPECIAL',
}

export const StrategySpecialistRiskLevelCN: Record<StrategySpecialistRiskLevel, string> = {
  [StrategySpecialistRiskLevel.BRONZE_SILVER]: '倔强青铜&秩序白银',
  [StrategySpecialistRiskLevel.FOOD_SAFETY_FOCUS]: '食安重点',
  [StrategySpecialistRiskLevel.PRODUCT_UNDERCOOKED]: '产品不熟',
  [StrategySpecialistRiskLevel.SHOP_STORE]: '校园店',
  [StrategySpecialistRiskLevel.PLATINUM_HEAVENLY_CHAMPION]: '尊贵铂金至尊星耀传奇王者',
  [StrategySpecialistRiskLevel.SPECIAL]: '特殊项目',
};

export enum StrategyAuditRiskLevel {
  PRIMARY_STORE = 'PRIMARY_STORE',
  RECHECK_STORE = 'RECHECK_STORE',
  HIGH_RISK_STORE = 'HIGH_RISK_STORE',
  SHOP_STORE = 'SHOP_STORE',
  SPECIAL_STAGE_STORE = 'SPECIAL_STAGE_STORE',
  CANDIDATE_CRITERIA = 'CANDIDATE_CRITERIA',
  SPECIAL_ITEM = 'SPECIAL_ITEM',
}

export const StrategyAuditRiskLevelCN: Record<StrategyAuditRiskLevel, string> = {
  [StrategyAuditRiskLevel.PRIMARY_STORE]: '青铜门店/青铜转白银门店',
  [StrategyAuditRiskLevel.RECHECK_STORE]: '复检门店',
  [StrategyAuditRiskLevel.HIGH_RISK_STORE]: '高风险门店',
  [StrategyAuditRiskLevel.SHOP_STORE]: '校内店',
  [StrategyAuditRiskLevel.SPECIAL_STAGE_STORE]: '阶段性特殊门店',
  [StrategyAuditRiskLevel.CANDIDATE_CRITERIA]: '候补标准',
  [StrategyAuditRiskLevel.SPECIAL_ITEM]: '特殊项目',
};

export enum ReportedItemType {
  JUDGE = 'JUDGE',
  SELECT = 'SELECT',
}

export interface ReportedItemDetail {
  /**
   * 基础任务id
   */
  baseTaskId?: number;
  /**
   * 提报时间
   */
  createTime?: string;
  /**
   * 提报人id
   */
  createUserId?: number;
  /**
   * 提报人id
   * 提报人名称
   */
  createUserName?: string;
  /**
   * 提报人id
   * 提报人角色信息
   */
  createUserRoles?: string[];
  /**
   * 一级标题选中0不合格1合格
   */
  firstActionJudge?: boolean;
  /**
   * id
   */
  id?: number;
  /**
   * 提报的图片连接
   */
  itemImageResources?: any[];
  /**
   * 提报的图片
   */
  itemImages?: string[];
  /**
   * 备注
   */
  remark?: string;
  /**
   * 二级文案选择的内容
   */
  secondActionSelected?: string;
  /**
   * 门店id
   */
  shopId?: string;
  /**
   * 门店名称
   */
  shopName?: string;
  /**
   * 巡检子类型
   */
  taskSubType?: TaskSubType;
  /**
   * 提报更改时间
   */
  updateTime?: string;
  [property: string]: any;
}
