import { get, post } from '@/http';

type GetDifferenceStrategyListReq = {
  pageNo: number;
  pageSize: number;
  shopIds: string[];
  groupId: number;
  status: string[];
  startTime: string;
  endTime: string;
};

export async function getDifferenceStrategyList(data: Partial<GetDifferenceStrategyListReq>) {
  return post('/tm-api/common/different-item-shop-confirm-task/page', data);
}

export async function getDifferenceStrategyDetail(params: { id: string }) {
  return get('/tm-api/common/different-item-shop-confirm-task/detail', {
    params,
  });
}
// 第三方审核 确认
export async function thirdPass(data: { id: number; thirdAuditRemark?: string }) {
  return post('/tm-api/corp/different-item-shop-confirm-task/third-pass', data);
}
// 第三方审核 驳回
export async function thirdReject(data: { id: number; thirdAuditRemark?: string }) {
  return post('/tm-api/corp/different-item-shop-confirm-task/third-reject', data);
}
