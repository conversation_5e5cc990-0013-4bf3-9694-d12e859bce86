export interface createCyclicalPlanPayload {
  /**
   * 循环模式，每日，每周，每月
   * 循环模式，每日，每周，每月;DAY-日;WEEK-周;MONTH-月;
   */
  cyclicalPatterns: CyclicalPatterns;
  /**
   * 任务发起日期
   */
  days?: number[] | null;
  /**
   * 直营检查表
   */
  directSheets: PlanSheet[] | null;
  /**
   * 需排班的消杀公司id
   */
  disinfectionCompanys: number[] | null;
  /**
   * 结束时间
   */
  endDate?: null | string;
  /**
   * 默认每个月最后一天24:00结束
   */
  endOnLastMonth: boolean | null;
  /**
   * 任务执行时长
   */
  executeDuration: Duration;
  /**
   * 任务执行时间,默认传00:00:00
   */
  executeTime?: null | string;
  /**
   * 日常消杀排班计划id
   */
  id?: number | null;
  /**
   * 加盟检查表
   */
  joinSheets: PlanSheet[] | null;
  /**
   * 计划名称
   */
  planName: null | string;
  /**
   * 计划状态
   */
  planStatus: boolean | null;
  /**
   * 同一检查表：0-否，1-是
   */
  sameSheet?: boolean | null;
  /**
   * 开始时间
   */
  startDate: null | string;
  [property: string]: any;
}

/**
 * 循环模式，每日，每周，每月
 * 循环模式，每日，每周，每月;DAY-日;WEEK-周;MONTH-月;
 */
export enum CyclicalPatterns {
  Day = 'DAY',
  Month = 'MONTH',
  Week = 'WEEK',
}

/**
 * 检查权重
 *
 * PlanSheet
 */
export interface PlanSheet {
  /**
   * 是否生成整改
   */
  generateRectification: boolean | null;
  /**
   * 点评人角色
   */
  reviewerRole?: number | null;
  /**
   * 权重
   * 检查表权重
   */
  weight?: number | null;
  /**
   * 检查表编号
   */
  workSheetId: number | null;
  [property: string]: any;
}

/**
 * 任务执行时长
 *
 * Duration
 */
export interface Duration {
  /**
   * The number of nanoseconds in the duration, expressed as a fraction of the
   * number of seconds. This is always positive, and never exceeds 999,999,999.
   */
  nanos?: number | null;
  /**
   * The number of seconds in the duration.
   */
  seconds?: number | null;
  [property: string]: any;
}
