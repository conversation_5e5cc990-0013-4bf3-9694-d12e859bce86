import { message } from 'antd';
import { get, post, put } from '..';
import { URLRequestPrefix } from '../config';
import { StoreType } from '@/constants/common';
import { StrategyTaskType } from '@/constants/strategy';

/**
 * TaskQueryRequest
 */
export interface GetTaskCenterListReq {
  endDate: null | string;
  groupId?: number | null;
  pageNo: number | null;
  pageSize: number | null;
  shopIds?: string[] | null;
  shopType?: StoreType;
  startDate: null | string;
  taskSubType?: StrategyTaskType;
  templateId?: number | null;
  [property: string]: any;
}

export const getTaskCenterList = async (data: GetTaskCenterListReq) =>
  await post(`${URLRequestPrefix.TM}/corp/task/pc-page/v2`, data);

// 任务补发
export interface ReissueTaskReq {
  /**
   * 批次号
   */
  batchId: number;
  /**
   * 补发的门店
   */
  shopIds: string[];
  [property: string]: any;
}

export const reissueTask = async (data: ReissueTaskReq) =>
  await post(`${URLRequestPrefix.TM}/corp/task/pc-page/reissue`, data);

// 任务批次作废

export interface CancelTaskReq {
  /**
   * 批次号
   */
  batchId: number;
  /**
   * 是否全部作废,true全部作废，false未执行的作废
   */
  isAllCancel?: boolean;
  /**
   * 任务种类
   */
  [property: string]: any;
}
export const cancelTask = async (data: CancelTaskReq) =>
  await post(`${URLRequestPrefix.TM}/corp/task/pc-page/cancel`, data);

// 获取任务批次详情
export interface GetTaskBatchDetailReq {
  id: number;
  taskType: string;
  [property: string]: any;
}
export const getTaskBatchDetail = async (data: GetTaskBatchDetailReq) =>
  await post(`${URLRequestPrefix.TM}/corp/task/pc-page/batchDetail`, data);

// 获取任务批次列表
export interface GetTaskBatchListReq {
  /**
   * 批次号
   */
  batchId: number;
  /**
   * 组织id
   */
  groupId?: number;
  pageNo: number;
  pageSize: number;
  /**
   * 门店id
   */
  shopIds?: string[];

  [property: string]: any;
}
export const getTaskBatchList = async (data: GetTaskBatchListReq) =>
  await post(`${URLRequestPrefix.TM}/corp/task/pc-page/detail`, data);

// 获取任务批次展开接口
export interface GetTaskCenterExpandListReq {
  /**
   * 批次号
   */
  batchId: number;
  /**
   * 组织id
   */
  groupId?: number;
  pageNo: number;
  pageSize: number;
  /**
   * 门店id
   */
  shopIds?: string[];
  /**
   * 任务种类
   */
  [property: string]: any;
}

export const getTaskCenterExpandList = async (data: GetTaskCenterExpandListReq) =>
  await post(`${URLRequestPrefix.TM}/corp/task/pc-page/extend/v2`, data);

export const cancelSingleTask = async (taskId) =>
  await post(`${URLRequestPrefix.TM}/corp/task/pc-page/singleCancel`, { taskId });

export const batchCancelSingleTask = async (taskIds: number[]) =>
  await post(`${URLRequestPrefix.TM}/corp/task/pc-page/batch/singleCancel`, { taskIds });

// 获取当个任务明细
export const getSingleTaskDetail = async (id: number) =>
  await post(`${URLRequestPrefix.TM}/corp/task/pc-page/itemDetail`, { id });

// 问题追踪列表
export const getIssueTrackList = async (baseTaskId: number) =>
  await post(`${URLRequestPrefix.TM}/corp/issue/listByReport`, { baseTaskId });

// 获取食安稽核到店辅导任务详情
export const getStragegyTutorTaskDetail = async (taskId: number) =>
  await get(`${URLRequestPrefix.TM}/common/patrol/task/food-safety/arrive-shop/${taskId}/info`);

// 非食安分页查询任务转办申请管理
export const getStrategyTransferList = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/task/transfer/manage-page`, data);

// 食安分页查询任务转办申请管理
export const getFoodSafetyStrategyTransferList = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/task/food-safety/transfer/manage-page`, data);

// 非食安导出
export const exportStrategyTransferList = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/task/transfer/manage-page/export`, data);

// 食安导出
export const exportFoodSafetyStrategyTransferList = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/task/food-safety/transfer/manage-page/export`, data);

// 转办任务详情
export const getStrategyTransferDetail = async (id: number) => {
  return await get(`${URLRequestPrefix.TM}/corp/task/transfer/detail`, { params: { id } });
};

// 驳回转办申请
export const rejectStrategyTransfer = async (data: { applicationId: number; taskId: number; reason: string }) =>
  await post(`${URLRequestPrefix.TM}/corp/task/transfer/reject`, data);
// 取消转办申请
export const cancelApproveTransfer = async (data: { applicationId: number; taskId: number }) =>
  await post(`${URLRequestPrefix.TM}/corp/task/transfer/cancel`, data);
// 申请驳回待接受转办
export const rejectApproveAcceptTransfer = async (data: { applicationId: number; taskId: number; reason: string }) =>
  await post(`${URLRequestPrefix.TM}/corp/task/transfer/reject-accept`, data);
// 同意转办申请
export const approveStrategyTransfer = async (data: { applicationId: number; newInspector: number; taskId: number }) =>
  await post(`${URLRequestPrefix.TM}/corp/task/transfer/approve`, data);
// 同意接受转派申请
export const approveAcceptTransfer = async (data: { applicationId: number; taskId: number }) =>
  await post(`${URLRequestPrefix.TM}/corp/task/transfer/accept`, data);

// 线上稽核任务列表
export const getCloudAuditTaskList = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/patrol-online-audit-task/page`, data);
// 线上稽核任务列表-管理员
export const getCloudAuditTaskListManage = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/patrol-online-audit-task/manage/page`, data);
// 线上稽核任务列表-导出
export const exportCloudAuditTaskList = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/patrol-online-audit-task/page/export`, data);
// 线上稽核任务列表-导出-管理员
export const exportCloudAuditTaskListManage = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/patrol-online-audit-task/manage/page/export`, data);
// 线上专员巡检专项列表
export const getCloudSpecialistTaskList = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/patrol-online-specialist-task/page`, data);
// 线上专员巡检专项列表-管理员
export const getCloudSpecialistTaskListManage = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/patrol-online-specialist-task/manage/page`, data);
// 线上专员巡检专项列表-导出
export const exportCloudSpecialistTaskList = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/patrol-online-specialist-task/page/export`, data);
// 线上专员巡检专项列表-导出-管理员
export const exportCloudSpecialistTaskListManage = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/patrol-online-specialist-task/manage/page/export`, data);

// 线上专员到店人员监管任务-处理人
export const getCloudUserTaskList = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/patrol-online-supervise-task/v2/user/page`, data);

// 线上专员到店人员监管任务-管理员
export const getCloudManagerTaskList = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/patrol-online-supervise-task/v2/manager/page`, data);
// 导出线上专员到店人员监管任务-处理人
export const exportUserTaskList = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/patrol-online-supervise-task/v2/user/page-export`, data);

// 导出线上专员到店人员监管任务-管理员
export const exportManagerTaskList = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/patrol-online-supervise-task/v2/manager/page-export`, data);
// 申请转派
export const applyTransfer = async (data: any) => await post(`${URLRequestPrefix.TM}/corp/task/transfer/create`, data);

// 申请转派
export const applyBatchTransfer = async (data: any) => {
  const res = (await post(`${URLRequestPrefix.TM}/corp/task/transfer/create-batch`, data)) as any;

  if (res?.length) {
    message.warning(res?.join('\n'));

    throw new Error('转派失败');
  }

  return res;
};

// 线上稽核任务列表(门店权限) 下一批任务id
export const nextCloudAuditTask = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/patrol-online-audit-task/next-batch-ids`, data);

// 线上专员巡检专项列表(门店权限) 下一批任务id
export const nextCloudSpecialistTask = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/patrol-online-specialist-task/next-batch-ids`, data);

// 线上专员到店人员监管任务(门店权限) 下一批任务idisManager
export const nextCloudSuperviseTask = async (data: any) => {
  return await post(
    `${URLRequestPrefix.TM}/corp/patrol-online-supervise-task/v2/${data?.isManager ? 'manager' : 'user'}/next-batch-ids`,
    data,
  );
};

/**
 * @description 巡检任务开始时间 埋点
 */
export const patrolTaskStartTimeBurying = async (data: { taskId: number }) =>
  await post(`${URLRequestPrefix.TM}/corp/patrol/task-start-time`, data);

// 提交巡检报告
export const submitPatrolReport = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/task/patrol/submit`, data);

// 查询基础任务状态（批量）
export const queryAllTaskStatus = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/common/task/find-base-task-status`, data);

// 食完到店辅导任务
export const getFoodSafetyTutorItemDetail = async (data: any) =>
  await get(`${URLRequestPrefix.TM}/common/report/worksheet/items/ref`, { params: data });
export const getFoodSafetyTutorTaskDetail = async (data: any) =>
  await get(`${URLRequestPrefix.TM}//common/report/worksheet/items/ref`, { params: data });
// 提交摄像头状态
export const submitCameraStatus = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/patrol-online-supervise-task/v2/camera-check`, data);
// 查看摄像头状态检查详情
export const getCameraSituation = async (data: { taskId: number }) =>
  await get(`${URLRequestPrefix.TM}/corp/patrol-online-supervise-task/v2/camera-check-detail`, { params: data });

// 查看关联审核报告列表
export const getRelatedAuditReports = async (data: { taskId: number }) =>
  await get(`${URLRequestPrefix.TM}/corp/patrol-online-supervise-task/v2/rel-report-list`, { params: data });

/** 获取下发失败任务明细列表 */
export function getErrorTaskList(data: { id: number; pageNo: number; pageSize: number; shopId?: string }) {
  return post(`${URLRequestPrefix.TM}/corp/task/pc-page/errorList`, data);
}

// 稽核排班任务列表
export const getFoodSafetyNormalRouteList = async (params: any) =>
  await get(`${URLRequestPrefix.TM}/corp/food-safety-normal-route/batch/page-list-pc`, { params });
// 根据批次时间获取路线id
export const getRouteIdsByBatchDate = async (batchDate: string) =>
  await get(`${URLRequestPrefix.TM}/corp/food-safety-normal-route/batch/${batchDate}/route-ids`);

// 根据批次id获取稽核路线
export const getAuditRouteByBatchId = async (batchId: number) =>
  await get(`${URLRequestPrefix.TM}/corp/food-safety-normal-route/batch/${batchId}/audit-route/`);

// 查询稽核排班详情列表
export const getAuditRouteDataPageList = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/food-safety-normal-route-batch/data/pageList`, data);

// 修改稽核排班详情任务执行人
export const modifyAuditRouteData = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/food-safety-normal-route-batch/data/modify`, data);
// 查看执行详情
export const getExecutionDetails = async (batchId: string) =>
  await get(`${URLRequestPrefix.TM}/corp/food-safety-normal-route/batch/${batchId}/execution-details`);
// 获取启用的稽核原因列表
export const getAuditEnableList = async () =>
  await get(`${URLRequestPrefix.TM}/corp/audit/route/unqualified-reason/enable-list`);
// 修改稽核人
export const modifyAuditPlanData = async (data: any) =>
  await put(`${URLRequestPrefix.TM}/corp/food-safety-normal-route/batch/modify-process-user`, data);
