import { get, post } from "..";
import type { PaginationParams } from '@/utils/pagination';
import { URLRequestPrefix } from "../config";
import { stringify } from "qs";

//通过门店ID查询设备通道列表
export const getCameraList = async (params: PaginationParams<{ shopId: number; }>) => await get(`${URLRequestPrefix.OM}/ai/patrol/shop/camera/list?${stringify(params)}`);

//查询正在播放的门店列表
export const getPatrolPlayShopList = async (data: { shopIds: string[]; }) => await post(`${URLRequestPrefix.OM}/patrol/play/shop/list`, data);

//保存检查项信息
export const savePatrolReportItem = async (data: any) => await post(`${URLRequestPrefix.OM}/corp/patrol/report/save/item`, data);

//批量保存当前报告观看视频云巡检时长
export const saveCurVideoWatchTime = async (data: {
  data: { taskId: any; videoTime: number; }[];
}) => await post(`${URLRequestPrefix.OM}/patrol/report/video/save/batch`, data);

//查询当前报告视频云巡检历史观看时长
export const getvideoHistorytTime = async (data: { idList: number[]; }) => await post(`${URLRequestPrefix.OM}/patrol/report/video`, data);

//海康打点事件请求
export const gethikDotEvents = async (data: any) => await post(`${URLRequestPrefix.OM}/reportPc/hik/dotEvent`, data);

// 获取视频取流认证信息
export const getVideoAuth = async () => await post(`${URLRequestPrefix.OM}/hikvision/ym/getVideoAuth`);

// 获取云眸客户端信息
export const getYmClientInfo = async () => await get(`${URLRequestPrefix.OM}/hikvision/ym/info`);

// 视频云巡检 巡检列表
export const queryPlans = async (data: any) => await post(`${URLRequestPrefix.OM}/corp/app/patrol/task/list`, data);

//查询门店详情
export const getShopDetail = async (shopId: number) => await get(`${URLRequestPrefix.OM}/common/shop/detail?shopId=${shopId}`);

//获取运营通组织门店数据
export const getGztGroupShopList = async (data: any) =>
  await post(`${URLRequestPrefix.OM}/common/getGroupShopList`, data);