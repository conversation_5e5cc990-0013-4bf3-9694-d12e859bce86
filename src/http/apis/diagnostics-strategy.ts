import { get, post } from '..';

export interface IDiagnosticListItem {
  type: string;
  configured: boolean;
  roleId?: number;
  diagnosticType: string;
  diagnosticCategoryName: string;
  diagnosticTypeName: string;
  diagnosticCategory: string;
}

/**
 * @description 获取诊断指标数据列表
 * @returns
 */
export async function getDiagnosticAllItem() {
  return get('/tm-api/corp/diagnostic/mapping/getAllItem') as unknown as Promise<IDiagnosticListItem[]>;
}

/**
 * @description 获取诊断指标临时模板列表
 * @param {object} params
 * @param {string} params.temporarilyTemplateId
 * @returns
 */
export function getTemporarilyTemplateDetail(params: { temporarilyTemplateId: string }) {
  return get('/tm-api/corp/template-patrol/temporarilyTemplate/detail', { params });
}

/**
 * 食安到店辅导
 */

interface ShopType {
  code: string;
  desc: string;
}

export async function queryPatrolShopType() {
  return get('/tm-api/corp/template-patrol/queryPatrolShopType') as unknown as Promise<ShopType[]>;
}

interface CreateTime {
  seconds: number;
  nanos: number;
}

interface UpdateTime {
  seconds: number;
  nanos: number;
}

interface Worksheet {
  id: number;
  sheetName: string;
  sheetStatus: string;
  allowOverdue: boolean;
  type: string;
  passScore: number;
  tags: number[];
  comment: string;
  usePermissionScope: string;
  createUserId: number;
  updateUserId: number;
  createTime: CreateTime;
  updateTime: UpdateTime;
  deleted: boolean;
  selfTemplateRefNum: number;
  patrolTemplateRefNum: number;
  reviewTemplateRefNum: number;
  issueTemplateRefNum: number;
}

interface RefItem {
  id: number;
  typeId: number;
  name: string;
  accentedTermTags: string[];
  status: string;
}

interface WorksheetItem {
  id: number;
  itemType: string;
  worksheetId: number;
  categoryId: number;
  content: string;
  remark: string;
  sopId: number;
  associationCheckItemId: number;
  refItem: RefItem;
  accentedTermTags: string[];
  displayScore: boolean;
  scoreType: string;
  scoringStandard: string;
  scoreMax: number;
  scoreMin: number;
  deductionConfigType: string;
  deductionConfigRate: number;
  sort: number;
}

interface IDiagnosticDetail {
  diagnosticType: string;
  bindType: string;
  worksheets: Worksheet[];
  worksheetItems: WorksheetItem[];
}

/**
 * @description 获取诊指标详情
 * @returns
 */
export async function getDiagnosticDetail(params: { templateId: number; type: string }) {
  return get('/tm-api/corp/diagnostic/mapping/detail', { params }) as unknown as Promise<IDiagnosticDetail>;
}

export type TDiagnosticMappingData = {
  templateId: number;
  type: string;
  temporarilyTemplateId?: string;
  bindType: string;
  worksheetIds?: number[];
  worksheetItemIds?: {
    worksheetId: number;
    worksheetItemId: number;
    worksheetType: string;
    bindWorksheetItemIds: number[];
  }[];
};

/**
 * @description 绑定诊断映射
 * @param data
 * @returns
 */
export async function bindDiagnosticMapping(data: TDiagnosticMappingData) {
  return post('/tm-api/corp/diagnostic/mapping/bind', data);
}

/**
 * @description 获取诊断指标分类和指标项树
 * @param id
 * @returns
 */
export async function getChecklistCategoriesItemTree(id: number) {
  return get('/tm-api/corp/worksheet/item-tree', { params: { id } });
}
