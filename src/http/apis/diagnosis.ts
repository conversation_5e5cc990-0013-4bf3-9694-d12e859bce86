import { get, post, put } from '..';
import { URLRequestPrefix } from '../config';

// 获取诊断任务检查表管理列表
export const getDiagnosisChecklistList = async () => await get(`${URLRequestPrefix.OM}/corp/diagnostic/mapping/list`);

// 诊断任务第一点评人
export const bindFirstReviewRole = async (data: { firstReviewUserRoleId: number; type: string }) =>
  await put(`${URLRequestPrefix.OM}/corp/diagnostic/mapping/change/first-review-user-role`, data);

// 获取诊断任务检查表设置详情
export const getDiagnosisChecklistConfig = async (type: string) =>
  await get(`${URLRequestPrefix.OM}/corp/diagnostic/mapping/detail`, { params: { type } });

// 绑定诊断任务检查表
export type BindDiagnosisChecklistConfigParams = {
  bindType: 'WORKSHEET' | 'WORKSHEET_ITEM';
  type: string;
  worksheetIds?: number[]; // 直接绑定检查表ID数组
  worksheetItemIds?: {
    worksheetId: number;
    worksheetItemId: number;
    worksheetType: string;
    bindWorksheetItemIds: number[];
  };
};
export const bindDiagnosisChecklistConfig = async (data: BindDiagnosisChecklistConfigParams) =>
  await post(`${URLRequestPrefix.OM}/corp/diagnostic/mapping/bind`, data);

// 诊断-门店完成情况
export type GetDiagnosisCompletionListParams = {
  groupId?: number;
  shopIds?: string[];
  startDate: string;
  endDate: string;
  alarmLevel?: string;
  roleIdList?: number[];
  patrolUserIds?: number[];
  workSheetId?: number;
};
export const getDiagnosisCompletionList = async (data: GetDiagnosisCompletionListParams) =>
  await post(`${URLRequestPrefix.OM}/statistic/shop/patrol/diagnostic/list`, data);

// 诊断-门店完成情况-汇总
export const getDiagnosisCompletionSummary = async (data: GetDiagnosisCompletionListParams) =>
  await post(`${URLRequestPrefix.OM}/statistic/shop/patrol/diagnostic/summary`, data);

// 诊断-门店完成情况-下载明细
export const exportDiagnosisCompletionList = async (data: GetDiagnosisCompletionListParams) =>
  await post(`${URLRequestPrefix.OM}/statistic/shop/patrol/diagnostic/export`, data);

// 诊断-巡检人完成情况
export type GetDiagnosisInspectorListParams = {
  startDate: string;
  endDate: string;
  alarmLevel?: string;
  roleIdList?: number[];
  superVisions?: number[];
  workSheetId?: number;
};
export const getDiagnosisInspectorList = async (data: GetDiagnosisInspectorListParams) =>
  await post(`${URLRequestPrefix.OM}/supervision/diagnostic/list`, data);

// 诊断-巡检人完成情况-导出
export const exportDiagnosisInspectorList = async (data: GetDiagnosisInspectorListParams) =>
  await post(`${URLRequestPrefix.OM}/supervision/list/diagnostic/export`, data);

// 诊断任务列表
export type GetDiagnosisTaskListParams = {
  pageNo: number;
  pageSize: number;
  level?: string;
  start: string;
  end: string;
};
export const getDiagnosisTaskList = async (params: GetDiagnosisTaskListParams) =>
  await get(`${URLRequestPrefix.OM}/corp/diagnostic/period/alarm/list`, { params });

export enum AlarmActionEnum {
  触发门店 = 'TRIGGER_SHOP',
  下发任务 = 'SEND_SHOP',
  未完成任务 = 'UNCOMPLETED',
}

// 诊断任务下发/未完成任务数
export type GetDiagnosisUncompletedTaskListParams = {
  pageNo: number;
  pageSize: number;
  level?: string;
  start: string;
  end: string;
  alarmAction: AlarmActionEnum.下发任务 | AlarmActionEnum.未完成任务;
};

export const getDiagnosisUncompletedTaskList = async (params: GetDiagnosisUncompletedTaskListParams) =>
  await get(`${URLRequestPrefix.OM}/corp/diagnostic/period/task/uncompleted`, { params });

// 诊断任务下发/未完成任务数-导出
export const exportDiagnosisUncompletedTaskList = async (params: GetDiagnosisUncompletedTaskListParams) =>
  await get(`${URLRequestPrefix.OM}/corp/diagnostic/period/task/uncompleted/export`, { params });

// 诊断任务触发门店列表-导出
export async function exportDiagnosisTriggerTaskList(params: GetDiagnosisUncompletedTaskListParams) {
  return await get(`${URLRequestPrefix.OM}/corp/diagnostic/period/alarm/detail/trigger/export`, { params });
}

// 诊断任务详情
export type getDiagnosisTaskDetailParams = {
  level: string;
  start: string;
  end: string;
};
export const getDiagnosisTaskDetail = async (params: getDiagnosisTaskDetailParams) =>
  await get(`${URLRequestPrefix.OM}/corp/diagnostic/period/alarm/detail`, { params });

// 撤回诊断任务
export type RevokeDiagnosisTaskParams = {
  level: string;
  start: string;
  end: string;
  shopIds?: string[]; // 无shopIds代表全部撤回
};
export const revokeDiagnosisTask = async (data: RevokeDiagnosisTaskParams) =>
  await post(`${URLRequestPrefix.OM}/corp/diagnostic/info/revoke`, data);

// 获取诊断任务检查表引用量
export type GetDiagnosisChecklistReferencesParams = {
  pageNo: number;
  pageSize: number;
  worksheetId: number;
};
export const getDiagnosisChecklistReferences = async (params: GetDiagnosisChecklistReferencesParams) =>
  await get(`${URLRequestPrefix.OM}/corp/diagnostic/mapping/list/by-worksheet`, { params });

// 获取诊断任务可撤回门店组织树
export type GetDiagnosisOrganizationTreeParams = {
  level: string;
  start: string;
  end: string;
};
export const getDiagnosisOrganizationTree = async (params: GetDiagnosisOrganizationTreeParams) =>
  await get(`${URLRequestPrefix.OM}/corp/diagnostic/period/alarm/tree`, { params });

// 查询诊断信息
export type DiagnosticInfo = {
  alarmLevel: 'RED' | 'ORANGE' | 'YELLOW' | 'GREEN';
  end: string;
  score: number;
  shop: {
    shopCode: string;
    shopId: string;
    shopName: string;
    shopType: number;
    status: number;
    valid: number;
    ymStoreId: string;
  };
  start: string;
  user: {
    alive: boolean;
    nickname: string;
    open: boolean;
    phone: string;
    userId: number;
  };
  id: number;
  taskId: number;
  applicant?: DiagnosticInfo['user'];
  processor?: DiagnosticInfo['user'];
};
export const getDiagnosticInfo = async (params: { taskId: number }) =>
  get<DiagnosticInfo>('/om-api/common/diagnostic/info/find-by-task-id', {
    params,
  });
