import type { PaginationParams } from '@/utils/pagination';
import { post } from '..';
import { URLRequestPrefix } from '../config';
import type { ImportEnum } from '@/pages/dataSource/import/enum';
import type { SatisfactionEnum } from '@/pages/dataSource/satisfaction/enum';
import type { SatisfactionParams } from '@/pages/dataSource/satisfaction';

// 数据源导入-友商数据相关接口
export const getMarketPage = async (data: PaginationParams) =>
  await post(`${URLRequestPrefix.OM}/common/datasource/partner-data/page`, data);

//删除导入
export const delImport = (id: number) =>
  post(`${URLRequestPrefix.OM}/common/datasource/rest/degree/delImportData/${id}`);

//导入模板
export const getImportTemplateUrl = (type: ImportEnum) =>
  post(`${URLRequestPrefix.OM}/common/datasource/rest/degree/template/url/${type}`, undefined, {
    responseType: 'blob'
  });

//下载失败列表
export const getFailUrl = (id: number) =>
  post(`${URLRequestPrefix.OM}/common/datasource/rest/degree/download/failData/${id}`);

//导入日志
export const getImportLog = (data: PaginationParams<{ type: ImportEnum }>) =>
  post(`${URLRequestPrefix.OM}/common/datasource/rest/degree/import/log/page`, data);

//新增导入
export const addImport = (data: { fileUrl: string; fileName: string; type: SatisfactionEnum }) =>
  post(`${URLRequestPrefix.OM}/common/datasource/rest/degree/import/excel`, data);

//导入成功的数据
export const getSatisfactionPage = async (data: PaginationParams<SatisfactionParams>) =>
  await post(`${URLRequestPrefix.OM}/common/datasource/rest/degree/success/page`, data);
