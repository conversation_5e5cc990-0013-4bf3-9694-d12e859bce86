import { get, post } from '..';
import { URLRequestPrefix } from '../config';
import { StoreType } from '@/constants/common';
import { NotFilledItemHandleType, StrategyCycleType, TaskSubType } from '@/constants/strategy';
import { TutorArriveShopType } from '@/constants/task';
import { StrategyReportStatus } from '@/constants/task-center';

export interface GetReportCenterListReq {
  /**
   * 开始时间
   */
  endDate: null | string;
  /**
   * 组织id
   */
  groupId?: number | null;
  isSubmitTime: boolean | null;
  pageNo: number | null;
  pageSize: number | null;
  /**
   * 报告是否通过
   */
  passed?: boolean | null;
  /**
   * 门店id
   */
  shopIds?: string[] | null;
  /**
   * 门店类型
   */
  shopType?: StoreType;
  /**
   * 开始时间
   */
  startDate: null | string;
  /**
   * 报告状态
   */
  status?: StrategyReportStatus;
  /**
   * 周期频次
   */
  taskCycleType?: StrategyCycleType;
  /**
   * 基础任务类型
   */
  taskSubType?: TaskSubType;
  /**
   * 任务处理人/巡检人
   */
  taskUserId?: number | null;
  /**
   * 基础模版id
   */
  templateId?: number | null;
  /**
   * 检查表id
   */
  worksheetId?: number | null;
  [property: string]: any;
}
export const getReportCenterList = async (data: GetReportCenterListReq) =>
  await post(`${URLRequestPrefix.TM}/corp/report/pc-page`, data);

export const exportSelfReport = async (data: GetReportCenterListReq) =>
  await post(`${URLRequestPrefix.TM}/corp/report/self/export`, data);
export const exportReport = async (data: GetReportCenterListReq) =>
  await post(`${URLRequestPrefix.TM}/corp/report/pc-page/export`, data);

// 获取策略任务基本信息
export type StrategyTaskInfo = {
  /**
   * 报告状态
   */
  reportStatus?: string;
  /**
   * 门店id
   */
  shopId?: null | string;
  /**
   * 门店名称
   */
  shopName?: null | string;
  /**
   * 总结
   */
  summary?: null | string;
  /**
   * 任务创建时间
   */
  taskCreateTime?: string;
  /**
   * 任务id
   */
  taskId?: number | null;
  /**
   * 任务名称
   */
  taskName?: null | string;
  /**
   * 任务报告id
   */
  taskReportId?: number | null;
  /**
   * 任务状态
   */
  taskStatus?: string;
  /**
   * 是否需要提交整改说明
   */
  hasNeedCommitIssueInstructions?: boolean;
  [property: string]: any;
};
export const getStrategyTaskInfo = async (params: { id: number }) =>
  await get(`${URLRequestPrefix.TM}/common/report/base-info`, { params });

export const getStrategyTaskWorksheet = async (params: {
  id: number;
  hasReview?: boolean;
  notFilledItemHandleType?: NotFilledItemHandleType;
}) => await get(`${URLRequestPrefix.TM}/common/report/worksheet/items/v2`, { params });

export interface SaveReviewItemDto {
  /**
   * 抄送人
   */
  copyUserIds?: number[];
  /**
   * 是否适用
   */
  hasApply?: boolean;
  itemJudge?: boolean;
  /**
   * 其他不合格原因
   */
  itemOtherReason?: string;
  itemReason?: string[];
  /**
   * 点评内容
   */
  itemRemark?: string;
  itemScore?: number;
  /**
   * 整改期限
   */
  reformLimit?: number;
  taskId: number;
  taskItemId: number;
  [property: string]: any;
}
export const saveReviewItem = async (data: SaveReviewItemDto) =>
  await post(`${URLRequestPrefix.TM}/corp/task/item/review`, data);

// 提交点评总结
export interface SubmitReviewSummaryDto {
  summary?: string;
  taskId: number;
  [property: string]: any;
}
export const submitReviewSummary = async (data: SubmitReviewSummaryDto) =>
  await post(`${URLRequestPrefix.TM}/corp/task/corp-summary`, data);

// 提交点评
export interface SubmitReviewDto {
  /**
   * 填充策略
   */
  notFilledItemHandleType: NotFilledItemHandleType;
  /**
   * 任务编号
   */
  baseTaskId: number;
  [property: string]: any;
}

export const submitReviewTask = async (data: SubmitReviewDto) =>
  await post(`${URLRequestPrefix.TM}/corp/task/review-submit`, data);

// 获取点评总结
export const getStrategyTaskReviewSummary = async (baseTaskId: number) =>
  await get(`${URLRequestPrefix.TM}/common/report/review-summary`, { params: { baseTaskId } });

// 获取报告汇总数据
export interface getTaskStatisticsDto {
  id: number;
  notFilledItemHandleType?: NotFilledItemHandleType;
}
export const getStrategyTaskStatistics = async (params: getTaskStatisticsDto) =>
  await get(`${URLRequestPrefix.TM}/common/report/detail-statistics`, { params });

// 获取诊断信息
export const getStrategyDiagnosticInfo = async (params: any) =>
  await get(`${URLRequestPrefix.TM}/common/diagnostic/info`, { params });

// 食安到店辅导报告列表
export const getStrategyTutorReportList = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/patrol/report/food-safety/arrive-shop/list-page`, data);

// 导出食安到店辅导报告列表
export const exportStrategyTutorReportList = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/patrol/report/food-safety/arrive-shop/export`, data);

// 食安稽核确认
export interface ConfirmTutorAuditReq {
  arriveShopRectifyRemark?: null | string;
  arriveShopRectifyType?: TutorArriveShopType;
  examSituationConfirm: boolean | null;
  examSituationConfirmRemark?: null | string;
  taskId: number | null;
  [property: string]: any;
}

export const confirmStrategyTutorAudit = async (data: ConfirmTutorAuditReq) =>
  await post(`${URLRequestPrefix.TM}/corp/patrol/report/food-safety/arrive-shop/online-audit-confirm`, data);

// 获取食安稽核确认详情
export const getStrategyConfirmTutorInfo = async (taskId: string) =>
  await get(`${URLRequestPrefix.TM}/corp/patrol/report/food-safety/arrive-shop/${taskId}/online-audit-confirm`);

// 考试情况详情接口
export const getStrategyTutorTaskExamDetail = async (taskId: number) =>
  await get(`${URLRequestPrefix.TM}/common/patrol/task/food-safety/arrive-shop/${taskId}/exam-situation/detail`);

// 获取任务详情
interface getTaskDetailProps {
  taskId: number;
  hasReview?: boolean;
  notFilledItemHandleType?: NotFilledItemHandleType;
}

export const getStrategyTaskDetail = async ({ taskId, hasReview, notFilledItemHandleType }: getTaskDetailProps) => {
  const info = await getStrategyTaskInfo({ id: taskId });
  const worksheets = await getStrategyTaskWorksheet({ id: taskId, hasReview, notFilledItemHandleType });
  const summarys = await getStrategyTaskReviewSummary(taskId);
  const statistics = await getStrategyTaskStatistics({ id: taskId });

  return {
    info,
    worksheets: worksheets?.data,
    summarys,
    statistics,
  };
};

// 保存巡检检查项
export const savePatrolCheckItem = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/task/patrol/item/save/v2`, data);
