import { post } from '..';
import { URLRequestPrefix } from '../config';

export interface DifficultShopListReq {
  candidateCreateAtEnd: string; // 候选人创建结束时间，ISO 8601 格式
  candidateCreateAtStart: string; // 候选人创建开始时间，ISO 8601 格式
  city: string; // 城市
  district: string; // 区域
  fullTime: boolean; // 是否全职
  groupId: number; // 分组ID
  pageNo: number; // 页码
  pageSize: number; // 每页大小
  province: string; // 省份
  shopId: string; // 门店ID
  shopName: string; // 门店名称
  shopOwnerName: string; // 门店所有者名称
  submitTimeEnd: string; // 提交结束时间，ISO 8601 格式
  submitTimeStart: string; // 提交开始时间，ISO 8601 格式
}
export interface DifficultShopListItem {
  candidateNum: number; // 候选人数
  city: string; // 城市
  createAt: string; // 创建时间，ISO 8601 格式
  createUser: number; // 创建用户ID
  createUserName: string; // 创建用户名称
  district: string; // 区域
  fullTime: boolean; // 是否全职
  id: number; // ID
  jobType: string; // 职位类型
  jobTypeName: string; // 职位类型名称
  province: string; // 省份
  requireNum: number; // 需求人数
  shopId: string; // 门店ID
  shopName: string; // 门店名称
  shopOwnerId: number; // 门店所有者ID
  shopOwnerName: string; // 门店所有者名称
  status: string; // 状态
  statusName: string; // 状态名称
  updateAt: string; // 更新时间，ISO 8601 格式
  updateUser: number; // 更新用户ID
  updateUserName: string; // 更新用户名称
}
/** 获取候选人列表 */
export function getDifficultShopList(data: DifficultShopListReq) {
  return post<PageRes<DifficultShopListItem>>(`${URLRequestPrefix.RM}/pageDifficultShop`, data);
}
